# 第5章：控制流与程序结构

## 章节概述

在掌握了Go语言的数据类型和函数设计后，我们现在要学习程序的"骨架"——控制流与程序结构。控制流决定了程序的执行路径，是将数据和函数组织成有意义程序的关键。如果说函数是程序的"肌肉"，那么控制流就是程序的"神经系统"，指挥着程序的每一个动作。

### 学习目标
- 掌握Go语言条件语句的高级用法和最佳实践
- 熟练运用循环语句处理各种数据结构和业务场景
- 理解goto和标签的合理使用场景
- 掌握程序结构设计的基本原则和模块化思想
- 学会使用控制流实现优雅的错误处理模式
- 初步了解并发控制流的基础概念

### 控制流在Go语言中的重要性
控制流是程序逻辑的核心，它们：
- **决定执行路径**：根据条件和数据状态选择不同的执行分支
- **控制程序节奏**：通过循环和跳转控制程序的执行流程
- **实现业务逻辑**：将复杂的业务规则转化为可执行的代码结构
- **优化程序性能**：合理的控制流设计能显著提高程序效率
- **增强代码可读性**：清晰的控制流让程序逻辑一目了然
- **连接函数与数据**：将前面学习的函数和数据类型组织成完整的程序

---

## 一、条件语句的高级用法

### 1.1 if语句的多种形式

Go语言的if语句简洁而强大，支持多种灵活的写法：

```go
package main

import (
    "fmt"
    "strconv"
    "strings"
)

// 用户权限检查示例
type User struct {
    ID       int
    Name     string
    Role     string
    IsActive bool
}

func checkUserPermission(user *User, action string) (bool, string) {
    // 形式1：基本if语句
    if user == nil {
        return false, "用户不存在"
    }
    
    // 形式2：if语句中的初始化
    if !user.IsActive {
        return false, "用户账户已被禁用"
    }
    
    // 形式3：复合条件判断
    if user.Role == "admin" || user.Role == "superuser" {
        return true, "管理员拥有所有权限"
    }
    
    // 形式4：if-else if-else链
    if action == "read" {
        return true, "所有用户都可以读取"
    } else if action == "write" && user.Role == "editor" {
        return true, "编辑者可以写入"
    } else if action == "delete" && user.Role == "admin" {
        return true, "只有管理员可以删除"
    } else {
        return false, fmt.Sprintf("用户 %s 没有 %s 权限", user.Name, action)
    }
}

// 配置解析示例
func parseConfig(configStr string) (map[string]interface{}, error) {
    config := make(map[string]interface{})
    
    // if语句中的变量声明和错误处理
    if lines := strings.Split(configStr, "\n"); len(lines) == 0 {
        return nil, fmt.Errorf("配置为空")
    } else {
        for _, line := range lines {
            line = strings.TrimSpace(line)
            
            // 嵌套if处理复杂逻辑
            if line != "" && !strings.HasPrefix(line, "#") {
                if parts := strings.SplitN(line, "=", 2); len(parts) == 2 {
                    key := strings.TrimSpace(parts[0])
                    value := strings.TrimSpace(parts[1])
                    
                    // 类型推断和转换
                    if intVal, err := strconv.Atoi(value); err == nil {
                        config[key] = intVal
                    } else if boolVal, err := strconv.ParseBool(value); err == nil {
                        config[key] = boolVal
                    } else {
                        config[key] = value
                    }
                }
            }
        }
    }
    
    return config, nil
}

func main() {
    // 测试用户权限检查
    users := []*User{
        {ID: 1, Name: "张三", Role: "admin", IsActive: true},
        {ID: 2, Name: "李四", Role: "editor", IsActive: true},
        {ID: 3, Name: "王五", Role: "user", IsActive: false},
    }
    
    actions := []string{"read", "write", "delete"}
    
    fmt.Println("=== 用户权限检查 ===")
    for _, user := range users {
        for _, action := range actions {
            allowed, message := checkUserPermission(user, action)
            status := "❌"
            if allowed {
                status = "✅"
            }
            fmt.Printf("%s %s - %s: %s\n", status, user.Name, action, message)
        }
        fmt.Println()
    }
    
    // 测试配置解析
    configStr := `
# 数据库配置
host=localhost
port=3306
ssl=true
timeout=30
# 应用配置
debug=false
name=MyApp
`
    
    fmt.Println("=== 配置解析 ===")
    if config, err := parseConfig(configStr); err != nil {
        fmt.Printf("配置解析失败: %v\n", err)
    } else {
        for key, value := range config {
            fmt.Printf("%s = %v (%T)\n", key, value, value)
        }
    }
}
```

### 1.2 switch语句的强大特性

Go的switch语句比其他语言更加灵活和强大：

```go
package main

import (
    "fmt"
    "reflect"
    "time"
)

// HTTP状态码处理
func handleHTTPStatus(statusCode int) string {
    switch statusCode {
    case 200:
        return "请求成功"
    case 201:
        return "资源创建成功"
    case 400, 401, 403: // 多个case值
        return "客户端错误"
    case 404:
        return "资源未找到"
    case 500, 502, 503: // 服务器错误
        return "服务器错误"
    default:
        return fmt.Sprintf("未知状态码: %d", statusCode)
    }
}

// 表达式switch
func categorizeNumber(num int) string {
    switch {
    case num < 0:
        return "负数"
    case num == 0:
        return "零"
    case num > 0 && num <= 10:
        return "小正数"
    case num > 10 && num <= 100:
        return "中等正数"
    default:
        return "大正数"
    }
}

// 类型switch
func processValue(value interface{}) string {
    switch v := value.(type) {
    case nil:
        return "空值"
    case bool:
        if v {
            return "布尔值: true"
        }
        return "布尔值: false"
    case int, int8, int16, int32, int64:
        return fmt.Sprintf("整数: %v", v)
    case uint, uint8, uint16, uint32, uint64:
        return fmt.Sprintf("无符号整数: %v", v)
    case float32, float64:
        return fmt.Sprintf("浮点数: %.2f", v)
    case string:
        if len(v) > 10 {
            return fmt.Sprintf("长字符串: %s...", v[:10])
        }
        return fmt.Sprintf("字符串: %s", v)
    case []int:
        return fmt.Sprintf("整数切片，长度: %d", len(v))
    case map[string]interface{}:
        return fmt.Sprintf("映射，键数量: %d", len(v))
    default:
        return fmt.Sprintf("未知类型: %s", reflect.TypeOf(v))
    }
}

// 时间处理switch
func getTimeOfDay(hour int) string {
    switch {
    case hour >= 6 && hour < 12:
        return "上午"
    case hour >= 12 && hour < 18:
        return "下午"
    case hour >= 18 && hour < 22:
        return "晚上"
    default:
        return "深夜"
    }
}

// fallthrough示例（谨慎使用）
func processGrade(grade string) {
    fmt.Printf("成绩 %s: ", grade)
    switch grade {
    case "A":
        fmt.Print("优秀 ")
        fallthrough
    case "B":
        fmt.Print("良好 ")
        fallthrough
    case "C":
        fmt.Print("及格 ")
        fmt.Println("- 通过考试")
    case "D", "F":
        fmt.Println("不及格 - 需要重考")
    default:
        fmt.Println("无效成绩")
    }
}

func main() {
    // HTTP状态码测试
    fmt.Println("=== HTTP状态码处理 ===")
    statusCodes := []int{200, 404, 500, 418}
    for _, code := range statusCodes {
        fmt.Printf("状态码 %d: %s\n", code, handleHTTPStatus(code))
    }
    
    // 数字分类测试
    fmt.Println("\n=== 数字分类 ===")
    numbers := []int{-5, 0, 3, 15, 150}
    for _, num := range numbers {
        fmt.Printf("%d: %s\n", num, categorizeNumber(num))
    }
    
    // 类型switch测试
    fmt.Println("\n=== 类型处理 ===")
    values := []interface{}{
        nil,
        true,
        42,
        3.14,
        "Hello, Go!",
        "这是一个很长的字符串，用来测试截断功能",
        []int{1, 2, 3, 4, 5},
        map[string]interface{}{"key1": "value1", "key2": "value2"},
        time.Now(),
    }
    
    for _, value := range values {
        fmt.Println(processValue(value))
    }
    
    // 时间分类测试
    fmt.Println("\n=== 时间分类 ===")
    hours := []int{8, 14, 20, 2}
    for _, hour := range hours {
        fmt.Printf("%d点: %s\n", hour, getTimeOfDay(hour))
    }
    
    // fallthrough示例
    fmt.Println("\n=== 成绩处理 (fallthrough示例) ===")
    grades := []string{"A", "B", "C", "D", "X"}
    for _, grade := range grades {
        processGrade(grade)
    }
}
```

---

## 二、循环语句的深入应用

### 2.1 for循环的三种形式

Go语言只有for一种循环语句，但它有多种灵活的形式：

```go
package main

import (
    "fmt"
    "math/rand"
    "time"
)

func main() {
    // 形式1：传统的三段式for循环
    fmt.Println("=== 传统for循环 ===")
    fmt.Print("倒计时: ")
    for i := 5; i > 0; i-- {
        fmt.Printf("%d ", i)
        time.Sleep(200 * time.Millisecond)
    }
    fmt.Println("发射! 🚀")
    
    // 形式2：while风格的for循环
    fmt.Println("\n=== while风格循环 ===")
    sum := 0
    i := 1
    for sum < 100 {
        sum += i
        fmt.Printf("第%d次: sum = %d\n", i, sum)
        i++
    }
    
    // 形式3：无限循环
    fmt.Println("\n=== 无限循环示例 ===")
    counter := 0
    for {
        counter++
        if counter > 3 {
            fmt.Println("循环结束")
            break
        }
        fmt.Printf("无限循环第%d次\n", counter)
    }
    
    // 嵌套循环：九九乘法表
    fmt.Println("\n=== 九九乘法表 ===")
    for i := 1; i <= 9; i++ {
        for j := 1; j <= i; j++ {
            fmt.Printf("%d×%d=%d\t", j, i, i*j)
        }
        fmt.Println()
    }
    
    // 循环控制：continue和break
    fmt.Println("\n=== 循环控制示例 ===")
    fmt.Println("1-20中的偶数:")
    for i := 1; i <= 20; i++ {
        if i%2 != 0 {
            continue // 跳过奇数
        }
        if i > 15 {
            break // 大于15时停止
        }
        fmt.Printf("%d ", i)
    }
    fmt.Println()
}
```

### 2.2 range的高级用法

range是Go语言的强大特性，可以遍历多种数据结构：

```go
package main

import (
    "fmt"
    "unicode/utf8"
)

func main() {
    // range遍历切片
    fmt.Println("=== range遍历切片 ===")
    fruits := []string{"苹果", "香蕉", "橙子", "葡萄"}
    
    // 获取索引和值
    for i, fruit := range fruits {
        fmt.Printf("索引%d: %s\n", i, fruit)
    }
    
    // 只获取索引
    fmt.Println("\n只获取索引:")
    for i := range fruits {
        fmt.Printf("索引: %d\n", i)
    }
    
    // 只获取值
    fmt.Println("\n只获取值:")
    for _, fruit := range fruits {
        fmt.Printf("水果: %s\n", fruit)
    }
    
    // range遍历映射
    fmt.Println("\n=== range遍历映射 ===")
    studentScores := map[string]int{
        "张三": 95,
        "李四": 87,
        "王五": 92,
        "赵六": 78,
    }
    
    for name, score := range studentScores {
        grade := "不及格"
        switch {
        case score >= 90:
            grade = "优秀"
        case score >= 80:
            grade = "良好"
        case score >= 70:
            grade = "中等"
        case score >= 60:
            grade = "及格"
        }
        fmt.Printf("%s: %d分 (%s)\n", name, score, grade)
    }
    
    // range遍历字符串
    fmt.Println("\n=== range遍历字符串 ===")
    text := "Hello, 世界! 🌍"
    
    fmt.Printf("字符串: %s\n", text)
    fmt.Printf("字节长度: %d\n", len(text))
    fmt.Printf("字符长度: %d\n", utf8.RuneCountInString(text))
    
    fmt.Println("\n按字节遍历:")
    for i := 0; i < len(text); i++ {
        fmt.Printf("字节%d: %d (%c)\n", i, text[i], text[i])
    }
    
    fmt.Println("\n按字符遍历:")
    for i, char := range text {
        fmt.Printf("位置%d: %c (Unicode: %U)\n", i, char, char)
    }
    
    // range遍历通道
    fmt.Println("\n=== range遍历通道 ===")
    ch := make(chan int, 5)
    
    // 发送数据到通道
    go func() {
        for i := 1; i <= 5; i++ {
            ch <- i * i
        }
        close(ch) // 关闭通道
    }()
    
    // 使用range遍历通道
    fmt.Println("从通道接收数据:")
    for value := range ch {
        fmt.Printf("接收到: %d\n", value)
    }
    
    // 复杂数据结构的遍历
    fmt.Println("\n=== 复杂数据结构遍历 ===")
    type Student struct {
        Name    string
        Scores  map[string]int
        Hobbies []string
    }
    
    students := []Student{
        {
            Name:    "张三",
            Scores:  map[string]int{"数学": 95, "英语": 87},
            Hobbies: []string{"篮球", "编程"},
        },
        {
            Name:    "李四",
            Scores:  map[string]int{"数学": 78, "英语": 92},
            Hobbies: []string{"音乐", "阅读", "游泳"},
        },
    }
    
    for i, student := range students {
        fmt.Printf("\n学生%d: %s\n", i+1, student.Name)
        
        fmt.Println("  成绩:")
        for subject, score := range student.Scores {
            fmt.Printf("    %s: %d分\n", subject, score)
        }
        
        fmt.Println("  爱好:")
        for j, hobby := range student.Hobbies {
            fmt.Printf("    %d. %s\n", j+1, hobby)
        }
    }
}
```

---

## 三、goto和标签的合理使用

虽然goto通常不推荐使用，但在某些特定场景下它能提供简洁的解决方案：

```go
package main

import (
    "fmt"
    "math/rand"
    "time"
)

// 示例1：跳出多层嵌套循环
func findTargetInMatrix(matrix [][]int, target int) (int, int, bool) {
    rows := len(matrix)
    if rows == 0 {
        return -1, -1, false
    }
    cols := len(matrix[0])
    
OuterLoop:
    for i := 0; i < rows; i++ {
        for j := 0; j < cols; j++ {
            if matrix[i][j] == target {
                fmt.Printf("在位置 (%d, %d) 找到目标值 %d\n", i, j, target)
                return i, j, true
            }
            if matrix[i][j] > target {
                // 如果当前值大于目标值，跳到下一行
                continue OuterLoop
            }
        }
    }
    
    return -1, -1, false
}

// 示例2：错误处理和资源清理
func processFiles(filenames []string) error {
    var files []*MockFile
    
    // 打开所有文件
    for _, filename := range filenames {
        file, err := openMockFile(filename)
        if err != nil {
            fmt.Printf("打开文件 %s 失败: %v\n", filename, err)
            goto cleanup
        }
        files = append(files, file)
        fmt.Printf("成功打开文件: %s\n", filename)
    }
    
    // 处理文件
    for _, file := range files {
        if err := file.process(); err != nil {
            fmt.Printf("处理文件 %s 失败: %v\n", file.name, err)
            goto cleanup
        }
        fmt.Printf("成功处理文件: %s\n", file.name)
    }
    
    // 正常完成
    fmt.Println("所有文件处理完成")
    goto cleanup

cleanup:
    // 清理资源
    fmt.Println("开始清理资源...")
    for _, file := range files {
        if file != nil {
            file.close()
            fmt.Printf("关闭文件: %s\n", file.name)
        }
    }
    return nil
}

// 模拟文件结构
type MockFile struct {
    name string
}

func openMockFile(filename string) (*MockFile, error) {
    // 模拟文件打开失败
    if filename == "bad.txt" {
        return nil, fmt.Errorf("文件不存在")
    }
    return &MockFile{name: filename}, nil
}

func (f *MockFile) process() error {
    // 模拟处理失败
    if f.name == "error.txt" {
        return fmt.Errorf("处理过程中发生错误")
    }
    return nil
}

func (f *MockFile) close() {
    // 模拟关闭文件
}

// 示例3：状态机实现
func simpleStateMachine() {
    state := "start"
    input := []string{"login", "work", "logout", "end"}
    inputIndex := 0
    
    fmt.Println("=== 简单状态机 ===")
    
start:
    fmt.Printf("当前状态: %s\n", state)
    if inputIndex >= len(input) {
        goto end
    }
    
    currentInput := input[inputIndex]
    inputIndex++
    fmt.Printf("接收输入: %s\n", currentInput)
    
    switch state {
    case "start":
        if currentInput == "login" {
            state = "logged_in"
            goto start
        }
        goto error
        
    case "logged_in":
        if currentInput == "work" {
            state = "working"
            goto start
        } else if currentInput == "logout" {
            state = "logged_out"
            goto start
        }
        goto error
        
    case "working":
        if currentInput == "logout" {
            state = "logged_out"
            goto start
        }
        goto error
        
    case "logged_out":
        if currentInput == "end" {
            goto end
        } else if currentInput == "login" {
            state = "logged_in"
            goto start
        }
        goto error
    }

error:
    fmt.Printf("错误: 在状态 %s 下无法处理输入 %s\n", state, currentInput)
    goto end

end:
    fmt.Printf("状态机结束，最终状态: %s\n", state)
}

func main() {
    // 测试矩阵查找
    fmt.Println("=== 矩阵查找测试 ===")
    matrix := [][]int{
        {1, 4, 7, 11},
        {2, 5, 8, 12},
        {3, 6, 9, 16},
        {10, 13, 14, 17},
    }
    
    targets := []int{5, 15, 1, 17}
    for _, target := range targets {
        row, col, found := findTargetInMatrix(matrix, target)
        if found {
            fmt.Printf("✅ 找到 %d 在位置 (%d, %d)\n", target, row, col)
        } else {
            fmt.Printf("❌ 未找到 %d\n", target)
        }
    }
    
    // 测试文件处理
    fmt.Println("\n=== 文件处理测试 ===")
    filenames := []string{"file1.txt", "file2.txt", "error.txt", "file3.txt"}
    processFiles(filenames)
    
    // 测试状态机
    fmt.Println("\n=== 状态机测试 ===")
    simpleStateMachine()
}
```

---

## 四、程序结构设计原则

### 4.1 代码组织和模块化设计

```go
package main

import (
    "fmt"
    "log"
    "strings"
)

// 用户管理模块
type UserManager struct {
    users map[int]*User
    nextID int
}

type User struct {
    ID       int
    Username string
    Email    string
    Role     string
    IsActive bool
}

func NewUserManager() *UserManager {
    return &UserManager{
        users:  make(map[int]*User),
        nextID: 1,
    }
}

func (um *UserManager) CreateUser(username, email, role string) (*User, error) {
    // 输入验证
    if err := um.validateUserInput(username, email, role); err != nil {
        return nil, err
    }

    // 检查用户名是否已存在
    if um.isUsernameExists(username) {
        return nil, fmt.Errorf("用户名 %s 已存在", username)
    }

    // 创建用户
    user := &User{
        ID:       um.nextID,
        Username: username,
        Email:    email,
        Role:     role,
        IsActive: true,
    }

    um.users[um.nextID] = user
    um.nextID++

    log.Printf("创建用户成功: %s (ID: %d)", username, user.ID)
    return user, nil
}

func (um *UserManager) validateUserInput(username, email, role string) error {
    if strings.TrimSpace(username) == "" {
        return fmt.Errorf("用户名不能为空")
    }

    if len(username) < 3 {
        return fmt.Errorf("用户名长度至少3个字符")
    }

    if !strings.Contains(email, "@") {
        return fmt.Errorf("邮箱格式不正确")
    }

    validRoles := []string{"admin", "user", "guest"}
    roleValid := false
    for _, validRole := range validRoles {
        if role == validRole {
            roleValid = true
            break
        }
    }

    if !roleValid {
        return fmt.Errorf("无效的角色: %s", role)
    }

    return nil
}

func (um *UserManager) isUsernameExists(username string) bool {
    for _, user := range um.users {
        if user.Username == username {
            return true
        }
    }
    return false
}

func (um *UserManager) GetUser(id int) (*User, error) {
    user, exists := um.users[id]
    if !exists {
        return nil, fmt.Errorf("用户ID %d 不存在", id)
    }
    return user, nil
}

func (um *UserManager) ListUsers() []*User {
    users := make([]*User, 0, len(um.users))
    for _, user := range um.users {
        users = append(users, user)
    }
    return users
}

func (um *UserManager) DeactivateUser(id int) error {
    user, err := um.GetUser(id)
    if err != nil {
        return err
    }

    user.IsActive = false
    log.Printf("用户 %s (ID: %d) 已被停用", user.Username, id)
    return nil
}

// 权限管理模块
type PermissionManager struct {
    rolePermissions map[string][]string
}

func NewPermissionManager() *PermissionManager {
    pm := &PermissionManager{
        rolePermissions: make(map[string][]string),
    }

    // 初始化默认权限
    pm.rolePermissions["admin"] = []string{"read", "write", "delete", "manage_users"}
    pm.rolePermissions["user"] = []string{"read", "write"}
    pm.rolePermissions["guest"] = []string{"read"}

    return pm
}

func (pm *PermissionManager) HasPermission(role, permission string) bool {
    permissions, exists := pm.rolePermissions[role]
    if !exists {
        return false
    }

    for _, p := range permissions {
        if p == permission {
            return true
        }
    }
    return false
}

func (pm *PermissionManager) AddPermission(role, permission string) {
    if permissions, exists := pm.rolePermissions[role]; exists {
        // 检查权限是否已存在
        for _, p := range permissions {
            if p == permission {
                return // 权限已存在
            }
        }
        pm.rolePermissions[role] = append(permissions, permission)
    } else {
        pm.rolePermissions[role] = []string{permission}
    }

    log.Printf("为角色 %s 添加权限: %s", role, permission)
}

// 应用程序主控制器
type Application struct {
    userManager       *UserManager
    permissionManager *PermissionManager
}

func NewApplication() *Application {
    return &Application{
        userManager:       NewUserManager(),
        permissionManager: NewPermissionManager(),
    }
}

func (app *Application) Run() {
    fmt.Println("=== 用户管理系统 ===")

    // 创建用户
    users := []struct {
        username, email, role string
    }{
        {"admin", "<EMAIL>", "admin"},
        {"john", "<EMAIL>", "user"},
        {"guest", "<EMAIL>", "guest"},
        {"", "<EMAIL>", "user"}, // 无效用户名
        {"bob", "invalid-email", "user"},    // 无效邮箱
    }

    fmt.Println("\n--- 创建用户 ---")
    for _, userData := range users {
        user, err := app.userManager.CreateUser(userData.username, userData.email, userData.role)
        if err != nil {
            fmt.Printf("❌ 创建用户失败: %v\n", err)
        } else {
            fmt.Printf("✅ 创建用户成功: %s (ID: %d, 角色: %s)\n",
                       user.Username, user.ID, user.Role)
        }
    }

    // 列出所有用户
    fmt.Println("\n--- 用户列表 ---")
    allUsers := app.userManager.ListUsers()
    for _, user := range allUsers {
        status := "激活"
        if !user.IsActive {
            status = "停用"
        }
        fmt.Printf("ID: %d, 用户名: %s, 邮箱: %s, 角色: %s, 状态: %s\n",
                   user.ID, user.Username, user.Email, user.Role, status)
    }

    // 权限检查
    fmt.Println("\n--- 权限检查 ---")
    permissions := []string{"read", "write", "delete", "manage_users"}

    for _, user := range allUsers {
        fmt.Printf("\n用户 %s (%s) 的权限:\n", user.Username, user.Role)
        for _, permission := range permissions {
            hasPermission := app.permissionManager.HasPermission(user.Role, permission)
            status := "❌"
            if hasPermission {
                status = "✅"
            }
            fmt.Printf("  %s %s\n", status, permission)
        }
    }

    // 停用用户
    fmt.Println("\n--- 停用用户 ---")
    if err := app.userManager.DeactivateUser(2); err != nil {
        fmt.Printf("停用用户失败: %v\n", err)
    }
}

func main() {
    app := NewApplication()
    app.Run()
}
```

---

## 五、错误处理的控制流模式

### 5.1 Early Return模式和错误传播

```go
package main

import (
    "fmt"
    "strconv"
    "strings"
)

// 银行账户示例
type BankAccount struct {
    ID      string
    Balance float64
    IsLocked bool
}

type Bank struct {
    accounts map[string]*BankAccount
}

func NewBank() *Bank {
    return &Bank{
        accounts: make(map[string]*BankAccount),
    }
}

// Early Return模式示例
func (b *Bank) Transfer(fromID, toID string, amount float64) error {
    // 参数验证 - early return
    if amount <= 0 {
        return fmt.Errorf("转账金额必须大于0")
    }

    if fromID == toID {
        return fmt.Errorf("不能向自己转账")
    }

    // 获取源账户 - early return
    fromAccount, exists := b.accounts[fromID]
    if !exists {
        return fmt.Errorf("源账户 %s 不存在", fromID)
    }

    if fromAccount.IsLocked {
        return fmt.Errorf("源账户 %s 已被锁定", fromID)
    }

    // 获取目标账户 - early return
    toAccount, exists := b.accounts[toID]
    if !exists {
        return fmt.Errorf("目标账户 %s 不存在", toID)
    }

    if toAccount.IsLocked {
        return fmt.Errorf("目标账户 %s 已被锁定", toID)
    }

    // 余额检查 - early return
    if fromAccount.Balance < amount {
        return fmt.Errorf("账户 %s 余额不足，当前余额: %.2f，转账金额: %.2f",
                         fromID, fromAccount.Balance, amount)
    }

    // 执行转账
    fromAccount.Balance -= amount
    toAccount.Balance += amount

    fmt.Printf("转账成功: %s -> %s, 金额: %.2f\n", fromID, toID, amount)
    return nil
}

// 错误包装和传播
func (b *Bank) ProcessTransferRequest(request string) error {
    // 解析转账请求
    parts := strings.Split(request, ",")
    if len(parts) != 3 {
        return fmt.Errorf("转账请求格式错误: %s", request)
    }

    fromID := strings.TrimSpace(parts[0])
    toID := strings.TrimSpace(parts[1])
    amountStr := strings.TrimSpace(parts[2])

    amount, err := strconv.ParseFloat(amountStr, 64)
    if err != nil {
        return fmt.Errorf("解析转账金额失败: %w", err)
    }

    // 调用转账方法，错误会自动传播
    if err := b.Transfer(fromID, toID, amount); err != nil {
        return fmt.Errorf("转账处理失败: %w", err)
    }

    return nil
}

// 批量处理示例
func (b *Bank) ProcessBatchTransfers(requests []string) []error {
    var errors []error

    for i, request := range requests {
        if err := b.ProcessTransferRequest(request); err != nil {
            // 收集错误但继续处理
            wrappedErr := fmt.Errorf("第%d个请求处理失败: %w", i+1, err)
            errors = append(errors, wrappedErr)
            continue
        }
        fmt.Printf("第%d个转账请求处理成功\n", i+1)
    }

    return errors
}

// 创建账户的辅助方法
func (b *Bank) CreateAccount(id string, initialBalance float64) {
    b.accounts[id] = &BankAccount{
        ID:      id,
        Balance: initialBalance,
        IsLocked: false,
    }
}

func (b *Bank) LockAccount(id string) error {
    account, exists := b.accounts[id]
    if !exists {
        return fmt.Errorf("账户 %s 不存在", id)
    }
    account.IsLocked = true
    return nil
}

func (b *Bank) GetAccountInfo(id string) (*BankAccount, error) {
    account, exists := b.accounts[id]
    if !exists {
        return nil, fmt.Errorf("账户 %s 不存在", id)
    }
    return account, nil
}

func main() {
    bank := NewBank()

    // 创建测试账户
    bank.CreateAccount("A001", 1000.0)
    bank.CreateAccount("A002", 500.0)
    bank.CreateAccount("A003", 200.0)

    fmt.Println("=== 银行转账系统 ===")

    // 显示初始账户状态
    fmt.Println("\n--- 初始账户状态 ---")
    accountIDs := []string{"A001", "A002", "A003"}
    for _, id := range accountIDs {
        if account, err := bank.GetAccountInfo(id); err == nil {
            fmt.Printf("账户 %s: 余额 %.2f\n", account.ID, account.Balance)
        }
    }

    // 测试正常转账
    fmt.Println("\n--- 正常转账测试 ---")
    if err := bank.Transfer("A001", "A002", 100.0); err != nil {
        fmt.Printf("转账失败: %v\n", err)
    }

    // 测试各种错误情况
    fmt.Println("\n--- 错误情况测试 ---")
    errorTests := []struct {
        from, to string
        amount   float64
        desc     string
    }{
        {"A001", "A002", -50, "负数金额"},
        {"A001", "A001", 100, "自己转给自己"},
        {"A999", "A002", 100, "源账户不存在"},
        {"A001", "A999", 100, "目标账户不存在"},
        {"A003", "A001", 300, "余额不足"},
    }

    for _, test := range errorTests {
        fmt.Printf("\n测试: %s\n", test.desc)
        if err := bank.Transfer(test.from, test.to, test.amount); err != nil {
            fmt.Printf("❌ %v\n", err)
        } else {
            fmt.Printf("✅ 转账成功\n")
        }
    }

    // 锁定账户测试
    fmt.Println("\n--- 账户锁定测试 ---")
    bank.LockAccount("A002")
    if err := bank.Transfer("A001", "A002", 50); err != nil {
        fmt.Printf("❌ %v\n", err)
    }

    // 批量转账测试
    fmt.Println("\n--- 批量转账测试 ---")
    requests := []string{
        "A001, A003, 50",
        "A002, A001, 100",  // 这个会失败，因为A002被锁定
        "A003, A001, 30",
        "A001, A999, 20",   // 这个会失败，目标账户不存在
        "invalid request",   // 格式错误
    }

    errors := bank.ProcessBatchTransfers(requests)
    if len(errors) > 0 {
        fmt.Printf("\n批量处理完成，发生 %d 个错误:\n", len(errors))
        for _, err := range errors {
            fmt.Printf("❌ %v\n", err)
        }
    } else {
        fmt.Println("\n✅ 所有转账请求处理成功")
    }

    // 显示最终账户状态
    fmt.Println("\n--- 最终账户状态 ---")
    for _, id := range accountIDs {
        if account, err := bank.GetAccountInfo(id); err == nil {
            status := "正常"
            if account.IsLocked {
                status = "锁定"
            }
            fmt.Printf("账户 %s: 余额 %.2f (%s)\n", account.ID, account.Balance, status)
        }
    }
}
```

---

## 六、并发控制流基础

### 6.1 select语句与channel的结合使用

```go
package main

import (
    "fmt"
    "math/rand"
    "time"
)

// 任务处理器示例
func worker(id int, jobs <-chan int, results chan<- int, done chan<- bool) {
    for {
        select {
        case job, ok := <-jobs:
            if !ok {
                fmt.Printf("工作者 %d: 任务通道已关闭\n", id)
                done <- true
                return
            }

            fmt.Printf("工作者 %d: 开始处理任务 %d\n", id, job)

            // 模拟工作时间
            time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)

            result := job * job
            results <- result
            fmt.Printf("工作者 %d: 完成任务 %d，结果 %d\n", id, job, result)

        case <-time.After(2 * time.Second):
            fmt.Printf("工作者 %d: 超时，没有新任务\n", id)
            done <- true
            return
        }
    }
}

// 超时控制示例
func fetchDataWithTimeout(url string, timeout time.Duration) (string, error) {
    dataChan := make(chan string, 1)
    errorChan := make(chan error, 1)

    // 模拟数据获取
    go func() {
        // 模拟网络延迟
        delay := time.Duration(rand.Intn(3000)) * time.Millisecond
        time.Sleep(delay)

        if rand.Float32() < 0.3 { // 30%概率失败
            errorChan <- fmt.Errorf("获取数据失败: %s", url)
        } else {
            dataChan <- fmt.Sprintf("来自 %s 的数据", url)
        }
    }()

    select {
    case data := <-dataChan:
        return data, nil
    case err := <-errorChan:
        return "", err
    case <-time.After(timeout):
        return "", fmt.Errorf("获取数据超时: %s", url)
    }
}

// 多路复用示例
func multiplexer() {
    // 创建多个数据源
    source1 := make(chan string)
    source2 := make(chan string)
    source3 := make(chan string)

    // 启动数据生产者
    go func() {
        for i := 0; i < 3; i++ {
            time.Sleep(500 * time.Millisecond)
            source1 <- fmt.Sprintf("源1-数据%d", i+1)
        }
        close(source1)
    }()

    go func() {
        for i := 0; i < 3; i++ {
            time.Sleep(700 * time.Millisecond)
            source2 <- fmt.Sprintf("源2-数据%d", i+1)
        }
        close(source2)
    }()

    go func() {
        for i := 0; i < 3; i++ {
            time.Sleep(300 * time.Millisecond)
            source3 <- fmt.Sprintf("源3-数据%d", i+1)
        }
        close(source3)
    }()

    // 多路复用处理
    activeChannels := 3
    for activeChannels > 0 {
        select {
        case data, ok := <-source1:
            if !ok {
                fmt.Println("源1已关闭")
                source1 = nil
                activeChannels--
            } else {
                fmt.Printf("接收到: %s\n", data)
            }

        case data, ok := <-source2:
            if !ok {
                fmt.Println("源2已关闭")
                source2 = nil
                activeChannels--
            } else {
                fmt.Printf("接收到: %s\n", data)
            }

        case data, ok := <-source3:
            if !ok {
                fmt.Println("源3已关闭")
                source3 = nil
                activeChannels--
            } else {
                fmt.Printf("接收到: %s\n", data)
            }
        }
    }

    fmt.Println("所有数据源已关闭")
}

func main() {
    rand.Seed(time.Now().UnixNano())

    // 工作者池示例
    fmt.Println("=== 工作者池示例 ===")
    jobs := make(chan int, 10)
    results := make(chan int, 10)
    done := make(chan bool, 3)

    // 启动3个工作者
    for i := 1; i <= 3; i++ {
        go worker(i, jobs, results, done)
    }

    // 发送任务
    go func() {
        for i := 1; i <= 5; i++ {
            jobs <- i
            fmt.Printf("发送任务: %d\n", i)
        }
        close(jobs)
    }()

    // 收集结果
    go func() {
        for i := 0; i < 5; i++ {
            result := <-results
            fmt.Printf("收到结果: %d\n", result)
        }
    }()

    // 等待所有工作者完成
    for i := 0; i < 3; i++ {
        <-done
    }

    // 超时控制示例
    fmt.Println("\n=== 超时控制示例 ===")
    urls := []string{
        "http://api1.example.com",
        "http://api2.example.com",
        "http://api3.example.com",
    }

    for _, url := range urls {
        data, err := fetchDataWithTimeout(url, 2*time.Second)
        if err != nil {
            fmt.Printf("❌ %s: %v\n", url, err)
        } else {
            fmt.Printf("✅ %s: %s\n", url, data)
        }
    }

    // 多路复用示例
    fmt.Println("\n=== 多路复用示例 ===")
    multiplexer()
}
```

---

## 七、常见问题与解决方案

### 7.1 控制流的常见陷阱

```go
package main

import (
    "fmt"
    "time"
)

// 陷阱1：switch的fallthrough误用
func demonstrateFallthroughTrap() {
    fmt.Println("=== switch fallthrough陷阱 ===")

    grade := "B"

    // 错误示例：意外的fallthrough
    fmt.Println("错误示例:")
    switch grade {
    case "A":
        fmt.Println("优秀")
        // 忘记break，但Go默认不会fallthrough
    case "B":
        fmt.Println("良好")
        fallthrough // 这里会继续执行下一个case
    case "C":
        fmt.Println("及格")
    default:
        fmt.Println("不及格")
    }

    // 正确示例
    fmt.Println("\n正确示例:")
    switch grade {
    case "A":
        fmt.Println("优秀")
    case "B":
        fmt.Println("良好")
    case "C":
        fmt.Println("及格")
    default:
        fmt.Println("不及格")
    }
}

// 陷阱2：range循环中的变量捕获
func demonstrateRangeVariableTrap() {
    fmt.Println("\n=== range变量捕获陷阱 ===")

    numbers := []int{1, 2, 3, 4, 5}

    // 错误示例：闭包捕获循环变量
    fmt.Println("错误示例:")
    var funcs []func() int
    for _, num := range numbers {
        funcs = append(funcs, func() int {
            return num // 所有闭包都会捕获最后的num值
        })
    }

    for i, fn := range funcs {
        fmt.Printf("函数%d返回: %d\n", i, fn())
    }

    // 正确示例：创建局部变量副本
    fmt.Println("\n正确示例:")
    funcs = nil
    for _, num := range numbers {
        num := num // 创建局部变量副本
        funcs = append(funcs, func() int {
            return num
        })
    }

    for i, fn := range funcs {
        fmt.Printf("函数%d返回: %d\n", i, fn())
    }
}

// 陷阱3：无限循环和资源泄露
func demonstrateInfiniteLoopTrap() {
    fmt.Println("\n=== 无限循环陷阱 ===")

    // 错误示例：条件永远不会改变
    fmt.Println("模拟无限循环检测:")
    counter := 0
    maxIterations := 1000000

    for {
        counter++

        // 模拟某种条件检查
        if counter%100000 == 0 {
            fmt.Printf("迭代次数: %d\n", counter)
        }

        // 安全退出机制
        if counter >= maxIterations {
            fmt.Println("达到最大迭代次数，强制退出")
            break
        }

        // 模拟实际的退出条件
        if counter == 500000 {
            fmt.Println("找到退出条件，正常退出")
            break
        }
    }
}

// 陷阱4：defer在循环中的累积
func demonstrateDeferInLoopTrap() {
    fmt.Println("\n=== defer在循环中的陷阱 ===")

    // 错误示例：defer在循环中累积
    fmt.Println("错误示例（模拟）:")
    fmt.Println("如果在循环中使用defer，所有defer会在函数结束时执行")

    // 正确示例：使用匿名函数
    fmt.Println("\n正确示例:")
    for i := 0; i < 3; i++ {
        func(index int) {
            fmt.Printf("开始处理索引 %d\n", index)
            defer fmt.Printf("完成处理索引 %d\n", index)

            // 模拟一些处理
            time.Sleep(100 * time.Millisecond)
        }(i)
    }
}

// 陷阱5：select的非确定性
func demonstrateSelectNonDeterminism() {
    fmt.Println("\n=== select非确定性陷阱 ===")

    ch1 := make(chan string, 1)
    ch2 := make(chan string, 1)

    // 同时向两个通道发送数据
    ch1 <- "来自通道1"
    ch2 <- "来自通道2"

    fmt.Println("多次select，观察随机性:")
    for i := 0; i < 5; i++ {
        select {
        case msg1 := <-ch1:
            fmt.Printf("第%d次: %s\n", i+1, msg1)
            ch1 <- "来自通道1" // 重新放入
        case msg2 := <-ch2:
            fmt.Printf("第%d次: %s\n", i+1, msg2)
            ch2 <- "来自通道2" // 重新放入
        default:
            fmt.Printf("第%d次: 没有数据可读\n", i+1)
        }
    }
}

func main() {
    demonstrateFallthroughTrap()
    demonstrateRangeVariableTrap()
    demonstrateInfiniteLoopTrap()
    demonstrateDeferInLoopTrap()
    demonstrateSelectNonDeterminism()
}
```

---

## 八、学习检验

完成以下练习来验证你对Go控制流的掌握：

### 练习1：状态机实现
```go
package main

import "fmt"

// TODO: 实现一个自动售货机状态机
// 状态：空闲、选择商品、投币、找零、出货
// 事件：投币、选择商品、取消、确认
// 要求：使用switch语句实现状态转换逻辑

type VendingMachine struct {
    // TODO: 定义状态机结构
}

func main() {
    // TODO: 测试状态机
}
```

### 练习2：复杂条件判断优化
```go
package main

import "fmt"

// TODO: 优化以下复杂的条件判断逻辑
func calculateDiscount(customerType string, orderAmount float64, isVIP bool, seasonalPromo bool) float64 {
    // 当前代码有很多嵌套的if语句，需要优化
    // 要求：使用switch、early return等技巧简化逻辑

    if customerType == "regular" {
        if orderAmount > 1000 {
            if isVIP {
                if seasonalPromo {
                    return 0.25
                } else {
                    return 0.20
                }
            } else {
                if seasonalPromo {
                    return 0.15
                } else {
                    return 0.10
                }
            }
        } else if orderAmount > 500 {
            // ... 更多嵌套逻辑
        }
    } else if customerType == "premium" {
        // ... 更多嵌套逻辑
    }

    return 0.0
}

func main() {
    // TODO: 测试优化后的函数
}
```

### 练习3：循环优化和性能
```go
package main

import "fmt"

// TODO: 优化以下算法的性能
// 1. 找出数组中的重复元素
// 2. 计算矩阵的转置
// 3. 实现高效的字符串搜索

func findDuplicates(numbers []int) []int {
    // TODO: 实现高效的重复元素查找
    return nil
}

func transposeMatrix(matrix [][]int) [][]int {
    // TODO: 实现矩阵转置
    return nil
}

func searchPattern(text, pattern string) []int {
    // TODO: 实现字符串模式搜索，返回所有匹配位置
    return nil
}

func main() {
    // TODO: 测试优化后的算法
}
```

### 练习4：并发控制流应用
```go
package main

import (
    "fmt"
    "time"
)

// TODO: 实现一个并发的网页爬虫
// 要求：
// 1. 使用goroutine并发处理
// 2. 使用select控制超时
// 3. 使用channel进行通信
// 4. 实现优雅的关闭机制

type Crawler struct {
    // TODO: 定义爬虫结构
}

func main() {
    // TODO: 测试并发爬虫
}
```

### 练习5：综合应用 - 任务调度器
```go
package main

import (
    "fmt"
    "time"
)

// TODO: 实现一个任务调度器
// 功能：
// 1. 支持定时任务
// 2. 支持延迟任务
// 3. 支持任务优先级
// 4. 支持任务取消
// 5. 支持并发执行

type Task struct {
    // TODO: 定义任务结构
}

type Scheduler struct {
    // TODO: 定义调度器结构
}

func main() {
    // TODO: 测试任务调度器
}
```

---

## 九、下一步指引

恭喜你！完成本章学习后，你已经掌握了Go语言控制流和程序结构设计的核心概念。

### 你现在具备了：
- ✅ 熟练掌握Go语言条件语句的高级用法
- ✅ 深入理解循环语句和range的强大功能
- ✅ 了解goto和标签的合理使用场景
- ✅ 掌握程序结构设计和模块化原则
- ✅ 熟练运用错误处理的控制流模式
- ✅ 初步了解并发控制流的基础概念
- ✅ 能够避免常见的控制流陷阱
- ✅ 具备编写清晰、高效控制流代码的能力

### 接下来的学习路径：
进入**第6章：包管理与模块系统**，我们将学习：
- Go Modules的深入使用和依赖管理
- 包的设计原则和最佳实践
- 模块版本控制和发布流程
- 包的导入机制和可见性规则

### 学习建议：
1. **多练习控制流组合**：在实际项目中灵活运用各种控制流语句
2. **关注代码可读性**：清晰的控制流让程序逻辑一目了然
3. **避免过度嵌套**：使用early return等技巧简化复杂逻辑
4. **理解Go的特色**：switch的灵活性、range的强大功能等
5. **注重错误处理**：Go的错误处理模式是语言的重要特色
6. **学习并发基础**：为后续深入学习并发编程做准备

控制流是程序的"神经系统"，决定了程序的执行路径和逻辑结构。掌握了控制流，你就能够将前面学习的数据类型和函数组织成完整、高效的程序。同时，良好的控制流设计也为后续学习包管理、接口设计、并发编程等高级特性奠定了坚实的基础。

准备好学习Go语言的包管理和模块系统了吗？让我们继续这个精彩的学习旅程！
```
```
