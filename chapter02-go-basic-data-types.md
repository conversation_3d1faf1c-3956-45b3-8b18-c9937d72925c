# 第2章：Go语言基础数据类型

## 章节概述

在成功搭建Go开发环境后，我们开始深入Go语言的核心——数据类型系统。数据类型是编程语言的基石，就像建筑材料决定了建筑物的特性一样，数据类型决定了程序能够处理什么样的数据以及如何处理这些数据。

### 学习目标
- 掌握Go语言所有基础数据类型的特点和使用场景
- 熟练运用多种变量声明方式
- 理解常量定义和iota枚举器的强大功能
- 掌握类型转换的规则和最佳实践
- 深入理解Go的类型推断机制
- 理解零值概念及其在Go中的重要作用

### 为什么基础数据类型如此重要？
基础数据类型是Go语言的DNA，它们：
- **决定内存使用**：不同类型占用不同的内存空间
- **影响程序性能**：选择合适的类型能优化程序效率
- **保证类型安全**：Go的强类型系统帮助我们避免很多运行时错误
- **奠定学习基础**：后续的复合类型、接口、并发等都建立在基础类型之上

---

## 一、Go语言基础数据类型详解

### 1.1 整数类型

Go提供了丰富的整数类型，满足不同精度和范围的需求：

| 类型 | 大小 | 范围 | 说明 |
|------|------|------|------|
| int8 | 1字节 | -128 到 127 | 有符号8位整数 |
| uint8 | 1字节 | 0 到 255 | 无符号8位整数（byte的别名） |
| int16 | 2字节 | -32,768 到 32,767 | 有符号16位整数 |
| uint16 | 2字节 | 0 到 65,535 | 无符号16位整数 |
| int32 | 4字节 | -2^31 到 2^31-1 | 有符号32位整数（rune的别名） |
| uint32 | 4字节 | 0 到 2^32-1 | 无符号32位整数 |
| int64 | 8字节 | -2^63 到 2^63-1 | 有符号64位整数 |
| uint64 | 8字节 | 0 到 2^64-1 | 无符号64位整数 |
| int | 平台相关 | 平台相关 | 32位系统为32位，64位系统为64位 |
| uint | 平台相关 | 平台相关 | 32位系统为32位，64位系统为64位 |
| uintptr | 平台相关 | 平台相关 | 存储指针的无符号整数 |

**代码示例**：
```go
package main

import (
    "fmt"
    "unsafe"
)

func main() {
    // 不同整数类型的声明和使用
    var a int8 = 127
    var b uint8 = 255
    var c int = 1000
    var d int64 = 9223372036854775807
    
    fmt.Printf("int8: %d, 大小: %d字节\n", a, unsafe.Sizeof(a))
    fmt.Printf("uint8: %d, 大小: %d字节\n", b, unsafe.Sizeof(b))
    fmt.Printf("int: %d, 大小: %d字节\n", c, unsafe.Sizeof(c))
    fmt.Printf("int64: %d, 大小: %d字节\n", d, unsafe.Sizeof(d))
    
    // 演示溢出（编译时会报错）
    // var overflow int8 = 128  // 编译错误：constant 128 overflows int8
}
```

### 1.2 浮点数类型

Go提供两种浮点数类型：

| 类型 | 大小 | 精度 | 说明 |
|------|------|------|------|
| float32 | 4字节 | 约7位十进制数 | 单精度浮点数 |
| float64 | 8字节 | 约15位十进制数 | 双精度浮点数（默认） |

**代码示例**：
```go
package main

import (
    "fmt"
    "math"
)

func main() {
    var f1 float32 = 3.14159
    var f2 float64 = 3.141592653589793
    
    fmt.Printf("float32: %.7f\n", f1)
    fmt.Printf("float64: %.15f\n", f2)
    
    // 科学计数法
    var scientific float64 = 1.23e4  // 12300
    fmt.Printf("科学计数法: %.2f\n", scientific)
    
    // 特殊值
    fmt.Printf("正无穷: %f\n", math.Inf(1))
    fmt.Printf("负无穷: %f\n", math.Inf(-1))
    fmt.Printf("非数字: %f\n", math.NaN())
}
```

### 1.3 布尔类型

布尔类型只有两个值：`true` 和 `false`。

**代码示例**：
```go
package main

import "fmt"

func main() {
    var isActive bool = true
    var isComplete bool  // 零值为false
    
    fmt.Printf("isActive: %t\n", isActive)
    fmt.Printf("isComplete: %t\n", isComplete)
    
    // 布尔运算
    result := isActive && !isComplete
    fmt.Printf("逻辑运算结果: %t\n", result)
    
    // 布尔类型不能与数字类型直接转换
    // var num int = int(isActive)  // 编译错误
}
```

### 1.4 字符串类型

Go中的字符串是不可变的字节序列，采用UTF-8编码。

**代码示例**：
```go
package main

import (
    "fmt"
    "unicode/utf8"
)

func main() {
    var name string = "Go语言"
    var empty string  // 零值为空字符串""
    
    fmt.Printf("字符串: %s\n", name)
    fmt.Printf("长度(字节): %d\n", len(name))
    fmt.Printf("长度(字符): %d\n", utf8.RuneCountInString(name))
    fmt.Printf("空字符串: '%s'\n", empty)
    
    // 字符串拼接
    greeting := "Hello, " + "World!"
    fmt.Printf("拼接结果: %s\n", greeting)
    
    // 多行字符串
    multiline := `这是一个
多行字符串
可以包含换行符`
    fmt.Println(multiline)
    
    // 字符串遍历
    for i, char := range name {
        fmt.Printf("索引%d: %c\n", i, char)
    }
}
```

---

## 二、变量声明的多种方式

### 2.1 使用var关键字

这是最基本的变量声明方式：

```go
package main

import "fmt"

func main() {
    // 方式1：声明并初始化
    var age int = 25
    var name string = "张三"
    
    // 方式2：声明后赋值
    var height int
    height = 175
    
    // 方式3：类型推断
    var weight = 70.5  // 自动推断为float64
    
    // 方式4：批量声明
    var (
        city    string = "北京"
        country string = "中国"
        code    int    = 100001
    )
    
    fmt.Printf("姓名: %s, 年龄: %d, 身高: %d, 体重: %.1f\n", 
               name, age, height, weight)
    fmt.Printf("城市: %s, 国家: %s, 代码: %d\n", 
               city, country, code)
}
```

### 2.2 短变量声明（:=）

这是Go语言特有的简洁声明方式，只能在函数内部使用：

```go
package main

import "fmt"

func main() {
    // 短变量声明
    name := "李四"
    age := 30
    isStudent := false
    
    // 多变量同时声明
    x, y, z := 1, 2, 3
    
    // 函数返回值的接收
    result, err := divide(10, 2)
    
    fmt.Printf("姓名: %s, 年龄: %d, 是否学生: %t\n", name, age, isStudent)
    fmt.Printf("坐标: (%d, %d, %d)\n", x, y, z)
    fmt.Printf("结果: %.2f, 错误: %v\n", result, err)
}

func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, fmt.Errorf("除数不能为零")
    }
    return a / b, nil
}
```

### 2.3 变量声明的最佳实践

```go
package main

import "fmt"

// 包级别变量使用var
var globalCounter int = 0

func main() {
    // 函数内部优先使用短变量声明
    localCounter := 10
    
    // 需要明确类型时使用var
    var percentage float32 = 85.5
    
    // 零值初始化时使用var
    var buffer []byte
    
    // 批量声明相关变量
    var (
        host = "localhost"
        port = 8080
        ssl  = false
    )
    
    fmt.Printf("全局计数器: %d, 本地计数器: %d\n", globalCounter, localCounter)
    fmt.Printf("百分比: %.1f%%\n", percentage)
    fmt.Printf("服务器: %s:%d, SSL: %t\n", host, port, ssl)
    fmt.Printf("缓冲区: %v\n", buffer)
}
```

---

## 三、常量定义和iota枚举器

### 3.1 常量的基本使用

常量在编译时确定，运行时不可修改：

```go
package main

import "fmt"

// 包级别常量
const Pi = 3.14159
const AppName = "Go学习应用"

func main() {
    // 函数内常量
    const MaxRetries = 3
    const Timeout = 30
    
    // 批量常量声明
    const (
        StatusOK    = 200
        StatusError = 500
        StatusNotFound = 404
    )
    
    // 类型化常量
    const TypedPi float64 = 3.14159
    
    fmt.Printf("π的值: %.5f\n", Pi)
    fmt.Printf("应用名称: %s\n", AppName)
    fmt.Printf("最大重试次数: %d\n", MaxRetries)
    fmt.Printf("HTTP状态码 - 成功: %d, 错误: %d\n", StatusOK, StatusError)
}
```

### 3.2 iota枚举器的强大功能

`iota` 是Go语言的常量生成器，在每个const声明块中从0开始：

```go
package main

import "fmt"

func main() {
    // 基本iota使用
    const (
        Monday = iota     // 0
        Tuesday           // 1
        Wednesday         // 2
        Thursday          // 3
        Friday            // 4
        Saturday          // 5
        Sunday            // 6
    )
    
    // 跳过某些值
    const (
        _ = iota          // 跳过0
        KB = 1 << (10 * iota)  // 1024
        MB                     // 1048576
        GB                     // 1073741824
    )
    
    // 表达式中的iota
    const (
        Read = 1 << iota  // 1 (二进制: 001)
        Write             // 2 (二进制: 010)
        Execute           // 4 (二进制: 100)
    )
    
    // 复杂的iota应用
    const (
        B = 1 << (10 * iota)
        KiB
        MiB
        GiB
        TiB
    )
    
    fmt.Printf("星期: 周一=%d, 周五=%d, 周日=%d\n", Monday, Friday, Sunday)
    fmt.Printf("存储单位: KB=%d, MB=%d, GB=%d\n", KB, MB, GB)
    fmt.Printf("权限: 读=%d, 写=%d, 执行=%d\n", Read, Write, Execute)
    fmt.Printf("二进制存储: 1KiB=%d, 1MiB=%d, 1GiB=%d\n", KiB, MiB, GiB)
}
```

---

## 四、类型转换规则和注意事项

### 4.1 显式类型转换

Go不支持隐式类型转换，所有转换都必须显式进行：

```go
package main

import (
    "fmt"
    "strconv"
)

func main() {
    // 数值类型转换
    var i int = 42
    var f float64 = float64(i)
    var u uint = uint(f)
    
    fmt.Printf("int: %d, float64: %.2f, uint: %d\n", i, f, u)
    
    // 精度丢失示例
    var bigFloat float64 = 123.456
    var smallInt int = int(bigFloat)  // 小数部分丢失
    fmt.Printf("原值: %.3f, 转换后: %d\n", bigFloat, smallInt)
    
    // 字符串和数值转换
    str := "123"
    num, err := strconv.Atoi(str)
    if err != nil {
        fmt.Printf("转换错误: %v\n", err)
    } else {
        fmt.Printf("字符串 '%s' 转换为数字: %d\n", str, num)
    }
    
    // 数字转字符串
    numStr := strconv.Itoa(456)
    fmt.Printf("数字 456 转换为字符串: '%s'\n", numStr)
    
    // 布尔值转换
    boolStr := strconv.FormatBool(true)
    fmt.Printf("布尔值转字符串: '%s'\n", boolStr)
}
```

### 4.2 类型转换的安全性

```go
package main

import (
    "fmt"
    "math"
)

func main() {
    // 溢出示例
    var bigNum int64 = math.MaxInt32 + 1
    var smallNum int32 = int32(bigNum)  // 溢出
    fmt.Printf("原值: %d, 转换后: %d (发生溢出)\n", bigNum, smallNum)
    
    // 安全的转换检查
    if bigNum <= math.MaxInt32 && bigNum >= math.MinInt32 {
        safeNum := int32(bigNum)
        fmt.Printf("安全转换: %d\n", safeNum)
    } else {
        fmt.Printf("值 %d 超出int32范围，无法安全转换\n", bigNum)
    }
    
    // 浮点数精度问题
    var precise float64 = 0.1 + 0.2
    fmt.Printf("0.1 + 0.2 = %.17f\n", precise)
    fmt.Printf("是否等于0.3: %t\n", precise == 0.3)
}
```

---

## 五、类型推断机制

Go的类型推断让代码更简洁，同时保持类型安全：

```go
package main

import "fmt"

func main() {
    // 基本类型推断
    auto1 := 42        // int
    auto2 := 3.14      // float64
    auto3 := "hello"   // string
    auto4 := true      // bool
    auto5 := 'A'       // rune (int32)
    
    fmt.Printf("auto1: %T = %v\n", auto1, auto1)
    fmt.Printf("auto2: %T = %v\n", auto2, auto2)
    fmt.Printf("auto3: %T = %v\n", auto3, auto3)
    fmt.Printf("auto4: %T = %v\n", auto4, auto4)
    fmt.Printf("auto5: %T = %v\n", auto5, auto5)
    
    // 复杂表达式的类型推断
    result := auto1 + int(auto2)  // int + int = int
    fmt.Printf("result: %T = %v\n", result, result)
    
    // 函数返回值的类型推断
    value, ok := getValue()
    fmt.Printf("value: %T = %v, ok: %T = %v\n", value, value, ok, ok)
}

func getValue() (int, bool) {
    return 100, true
}
```

---

## 六、零值概念和默认值

Go中每种类型都有零值，这是类型安全的重要保证：

```go
package main

import "fmt"

func main() {
    // 各种类型的零值
    var (
        zeroInt     int
        zeroFloat   float64
        zeroString  string
        zeroBool    bool
        zeroPointer *int
        zeroSlice   []int
        zeroMap     map[string]int
        zeroFunc    func()
    )
    
    fmt.Printf("int零值: %d\n", zeroInt)
    fmt.Printf("float64零值: %.1f\n", zeroFloat)
    fmt.Printf("string零值: '%s' (长度: %d)\n", zeroString, len(zeroString))
    fmt.Printf("bool零值: %t\n", zeroBool)
    fmt.Printf("指针零值: %v\n", zeroPointer)
    fmt.Printf("切片零值: %v (长度: %d)\n", zeroSlice, len(zeroSlice))
    fmt.Printf("映射零值: %v\n", zeroMap)
    fmt.Printf("函数零值: %v\n", zeroFunc)
    
    // 零值的实用性
    var counter int
    counter++  // 可以直接使用，因为零值是0
    fmt.Printf("计数器: %d\n", counter)
    
    var message string
    message += "Hello"  // 可以直接拼接，因为零值是空字符串
    fmt.Printf("消息: %s\n", message)
}
```

---

## 七、实践操作

### 7.1 综合示例：学生信息管理

```go
package main

import (
    "fmt"
    "strconv"
)

// 使用常量定义学生状态
const (
    StatusActive = iota
    StatusInactive
    StatusGraduated
    StatusSuspended
)

func main() {
    // 学生基本信息
    studentID := 20230001
    name := "张小明"
    age := uint8(20)
    height := 175.5
    isScholarship := true
    
    // 成绩信息
    var (
        mathScore    float32 = 95.5
        englishScore float32 = 88.0
        physicsScore float32 = 92.5
    )
    
    // 计算平均分
    averageScore := (mathScore + englishScore + physicsScore) / 3
    
    // 状态信息
    status := StatusActive
    
    // 输出学生信息
    fmt.Println("=== 学生信息管理系统 ===")
    fmt.Printf("学号: %d\n", studentID)
    fmt.Printf("姓名: %s\n", name)
    fmt.Printf("年龄: %d岁\n", age)
    fmt.Printf("身高: %.1fcm\n", height)
    fmt.Printf("奖学金: %t\n", isScholarship)
    fmt.Printf("数学成绩: %.1f\n", mathScore)
    fmt.Printf("英语成绩: %.1f\n", englishScore)
    fmt.Printf("物理成绩: %.1f\n", physicsScore)
    fmt.Printf("平均成绩: %.2f\n", averageScore)
    
    // 状态显示
    var statusText string
    switch status {
    case StatusActive:
        statusText = "在读"
    case StatusInactive:
        statusText = "休学"
    case StatusGraduated:
        statusText = "已毕业"
    case StatusSuspended:
        statusText = "停学"
    default:
        statusText = "未知状态"
    }
    fmt.Printf("学生状态: %s\n", statusText)
    
    // 类型转换示例
    studentIDStr := strconv.Itoa(studentID)
    fmt.Printf("学号(字符串): %s\n", studentIDStr)
    
    // 成绩等级判定
    var grade string
    switch {
    case averageScore >= 90:
        grade = "A"
    case averageScore >= 80:
        grade = "B"
    case averageScore >= 70:
        grade = "C"
    case averageScore >= 60:
        grade = "D"
    default:
        grade = "F"
    }
    fmt.Printf("成绩等级: %s\n", grade)
}
```

---

## 八、常见问题与解决方案

### 8.1 类型不匹配错误

**问题**：不同数值类型无法直接运算
```go
// 错误示例
var a int32 = 10
var b int64 = 20
// result := a + b  // 编译错误：类型不匹配
```

**解决方案**：
```go
var a int32 = 10
var b int64 = 20
result := int64(a) + b  // 显式转换
fmt.Printf("结果: %d\n", result)
```

### 8.2 字符串和数值转换错误

**问题**：字符串转数值时没有处理错误
```go
// 危险示例
str := "abc"
num, _ := strconv.Atoi(str)  // 忽略错误
fmt.Println(num)  // 输出0，但实际转换失败
```

**解决方案**：
```go
str := "abc"
num, err := strconv.Atoi(str)
if err != nil {
    fmt.Printf("转换失败: %v\n", err)
    return
}
fmt.Printf("转换成功: %d\n", num)
```

### 8.3 浮点数精度问题

**问题**：浮点数比较不准确
```go
// 问题示例
if 0.1 + 0.2 == 0.3 {
    fmt.Println("相等")
} else {
    fmt.Println("不相等")  // 实际输出
}
```

**解决方案**：
```go
import "math"

func floatEqual(a, b, tolerance float64) bool {
    return math.Abs(a - b) < tolerance
}

if floatEqual(0.1 + 0.2, 0.3, 1e-9) {
    fmt.Println("相等")
}
```

### 8.4 常量溢出问题

**问题**：常量值超出类型范围
```go
// 错误示例
// const BigNumber int8 = 300  // 编译错误：溢出
```

**解决方案**：
```go
const BigNumber int16 = 300  // 使用更大的类型
// 或者
const BigNumber = 300  // 无类型常量，使用时再确定类型
```

---

## 九、学习检验

完成以下练习来验证你对Go基础数据类型的掌握：

### 练习1：类型探索器
编写程序输出各种类型的信息：
```go
package main

import (
    "fmt"
    "unsafe"
)

func main() {
    // TODO: 声明以下类型的变量并输出其类型、大小和零值
    // int8, uint16, int32, uint64, float32, float64, bool, string
    
    // 示例：
    var example int8
    fmt.Printf("类型: %T, 大小: %d字节, 零值: %d\n", 
               example, unsafe.Sizeof(example), example)
    
    // 请完成其他类型...
}
```

### 练习2：温度转换器
实现摄氏度和华氏度的相互转换：
```go
package main

import "fmt"

func main() {
    // TODO: 实现以下功能
    // 1. 定义常量：冰点(0°C)和沸点(100°C)对应的华氏度
    // 2. 实现摄氏度转华氏度函数：F = C * 9/5 + 32
    // 3. 实现华氏度转摄氏度函数：C = (F - 32) * 5/9
    // 4. 测试转换功能
}
```

### 练习3：数据类型转换练习
```go
package main

import (
    "fmt"
    "strconv"
)

func main() {
    // TODO: 完成以下转换并处理可能的错误
    // 1. 将字符串"123.45"转换为float64
    // 2. 将float64值转换为int（观察精度丢失）
    // 3. 将布尔值true转换为字符串
    // 4. 尝试将"hello"转换为数字（处理错误）
}
```

### 练习4：iota枚举练习
```go
package main

import "fmt"

func main() {
    // TODO: 使用iota定义以下枚举
    // 1. 文件权限：无权限(0), 读(1), 写(2), 执行(4)
    // 2. 网络协议端口：HTTP(80), HTTPS(443), FTP(21), SSH(22)
    // 3. 存储单位：Byte(1), KB(1024), MB(1024*1024)...
}
```

### 练习5：综合应用
创建一个简单的商品管理程序：
```go
package main

import "fmt"

func main() {
    // TODO: 实现商品信息管理
    // 1. 定义商品属性：ID(int), 名称(string), 价格(float64), 库存(uint), 是否上架(bool)
    // 2. 使用iota定义商品分类常量
    // 3. 计算商品总价值（价格 * 库存）
    // 4. 实现价格的字符串格式化显示
    // 5. 处理库存不足的情况
}
```

---

## 十、下一步指引

恭喜你！完成本章学习后，你已经掌握了Go语言的基础数据类型系统。

### 你现在具备了：
- ✅ 深入理解Go的类型系统
- ✅ 熟练使用各种变量声明方式
- ✅ 掌握常量和iota的强大功能
- ✅ 理解类型转换的规则和安全性
- ✅ 掌握零值概念的重要作用

### 接下来的学习路径：
进入**第3章：复合数据类型详解**，我们将学习：
- 数组和切片（slice）的深入使用
- 映射（map）的操作和最佳实践
- 字符串处理和rune类型的应用
- 复合类型的内存布局和性能考虑

### 学习建议：
1. **多写代码**：基础数据类型是编程的基石，需要大量练习
2. **注意类型安全**：Go的强类型系统是优势，要善于利用
3. **理解内存模型**：不同类型的内存占用影响程序性能
4. **关注边界情况**：溢出、精度丢失等问题在实际开发中很重要

基础数据类型虽然简单，但它们是构建复杂程序的基石。扎实掌握这些基础知识，将为你后续学习复合类型、并发编程等高级特性打下坚实的基础。

准备好探索Go语言更丰富的数据结构了吗？让我们继续前进！
