# 第7章：指针与内存管理

## 章节概述

在掌握了Go语言的包管理与模块系统后，我们现在要深入学习Go语言的"内存世界"——指针与内存管理。如果说前面学习的内容是构建程序的"框架"，那么指针和内存管理就是程序运行的"血液循环系统"，它们决定了程序的性能、安全性和资源使用效率。

### 学习目标
- 深入理解指针的概念、使用方法和最佳实践
- 掌握Go语言的内存分配机制和内存布局
- 理解垃圾回收机制的工作原理和性能调优
- 学会识别和预防内存泄漏问题
- 掌握内存分析工具的使用方法
- 了解unsafe包的使用场景和注意事项
- 培养内存安全和性能优化的编程意识

### 指针与内存管理的重要性
指针与内存管理是系统级编程的核心，它们：
- **提高程序性能**：合理的内存使用能显著提升程序效率
- **保证内存安全**：Go的内存管理机制避免了常见的内存错误
- **支持高效数据结构**：指针是实现链表、树等数据结构的基础
- **优化资源使用**：理解内存分配有助于编写资源友好的程序
- **连接底层与高层**：将前面学习的包、函数等组织成高效的内存布局
- **支持并发编程**：为后续学习goroutine和channel奠定基础

---

## 一、指针的深入使用和内存地址操作

### 1.1 指针的基本概念和操作

```go
package main

import (
    "fmt"
    "unsafe"
)

func demonstratePointerBasics() {
    fmt.Println("=== 指针基础操作 ===")
    
    // 基本类型的指针操作
    var num int = 42
    var ptr *int = &num  // 取地址操作符 &
    
    fmt.Printf("变量 num 的值: %d\n", num)
    fmt.Printf("变量 num 的地址: %p\n", &num)
    fmt.Printf("指针 ptr 的值(地址): %p\n", ptr)
    fmt.Printf("指针 ptr 指向的值: %d\n", *ptr)  // 解引用操作符 *
    fmt.Printf("指针 ptr 本身的地址: %p\n", &ptr)
    
    // 通过指针修改值
    *ptr = 100
    fmt.Printf("通过指针修改后，num 的值: %d\n", num)
    
    // 指针的零值
    var nilPtr *int
    fmt.Printf("空指针的值: %v\n", nilPtr)
    fmt.Printf("空指针是否为nil: %t\n", nilPtr == nil)
    
    // 不同类型的指针
    var str string = "Hello, Go!"
    var strPtr *string = &str
    var slice []int = []int{1, 2, 3, 4, 5}
    var slicePtr *[]int = &slice
    
    fmt.Printf("\n=== 不同类型的指针 ===\n")
    fmt.Printf("字符串指针: %p, 值: %s\n", strPtr, *strPtr)
    fmt.Printf("切片指针: %p, 值: %v\n", slicePtr, *slicePtr)
    
    // 指针的大小
    fmt.Printf("\n=== 指针大小信息 ===\n")
    fmt.Printf("int 类型大小: %d 字节\n", unsafe.Sizeof(num))
    fmt.Printf("*int 指针大小: %d 字节\n", unsafe.Sizeof(ptr))
    fmt.Printf("string 类型大小: %d 字节\n", unsafe.Sizeof(str))
    fmt.Printf("*string 指针大小: %d 字节\n", unsafe.Sizeof(strPtr))
}

// 指针作为函数参数
func swapByValue(a, b int) {
    a, b = b, a
    fmt.Printf("函数内部交换后: a=%d, b=%d\n", a, b)
}

func swapByPointer(a, b *int) {
    *a, *b = *b, *a
    fmt.Printf("函数内部交换后: *a=%d, *b=%d\n", *a, *b)
}

func demonstratePointerParameters() {
    fmt.Println("\n=== 指针作为函数参数 ===")
    
    x, y := 10, 20
    fmt.Printf("交换前: x=%d, y=%d\n", x, y)
    
    // 值传递 - 不会影响原变量
    swapByValue(x, y)
    fmt.Printf("值传递后: x=%d, y=%d\n", x, y)
    
    // 指针传递 - 会影响原变量
    swapByPointer(&x, &y)
    fmt.Printf("指针传递后: x=%d, y=%d\n", x, y)
}

// 指针与数组、切片的关系
func demonstratePointerWithArraySlice() {
    fmt.Println("\n=== 指针与数组、切片 ===")
    
    // 数组指针
    arr := [5]int{1, 2, 3, 4, 5}
    arrPtr := &arr
    fmt.Printf("数组: %v\n", arr)
    fmt.Printf("数组指针指向的值: %v\n", *arrPtr)
    
    // 修改数组元素
    (*arrPtr)[0] = 100
    fmt.Printf("通过指针修改后的数组: %v\n", arr)
    
    // 切片的内部结构
    slice := []int{10, 20, 30, 40, 50}
    fmt.Printf("切片: %v\n", slice)
    fmt.Printf("切片长度: %d, 容量: %d\n", len(slice), cap(slice))
    
    // 切片元素的地址
    for i := 0; i < len(slice); i++ {
        fmt.Printf("slice[%d] 地址: %p, 值: %d\n", i, &slice[i], slice[i])
    }
    
    // 切片指针
    slicePtr := &slice
    fmt.Printf("切片指针: %p\n", slicePtr)
    (*slicePtr)[0] = 1000
    fmt.Printf("通过切片指针修改后: %v\n", slice)
}

func main() {
    demonstratePointerBasics()
    demonstratePointerParameters()
    demonstratePointerWithArraySlice()
}
```

### 1.2 指针的高级应用

```go
package main

import (
    "fmt"
    "reflect"
)

// 链表节点示例
type ListNode struct {
    Value int
    Next  *ListNode
}

// 链表结构
type LinkedList struct {
    Head *ListNode
    Size int
}

// 添加节点
func (ll *LinkedList) Add(value int) {
    newNode := &ListNode{Value: value}
    
    if ll.Head == nil {
        ll.Head = newNode
    } else {
        current := ll.Head
        for current.Next != nil {
            current = current.Next
        }
        current.Next = newNode
    }
    ll.Size++
}

// 删除节点
func (ll *LinkedList) Remove(value int) bool {
    if ll.Head == nil {
        return false
    }
    
    // 删除头节点
    if ll.Head.Value == value {
        ll.Head = ll.Head.Next
        ll.Size--
        return true
    }
    
    // 删除其他节点
    current := ll.Head
    for current.Next != nil {
        if current.Next.Value == value {
            current.Next = current.Next.Next
            ll.Size--
            return true
        }
        current = current.Next
    }
    
    return false
}

// 打印链表
func (ll *LinkedList) Print() {
    fmt.Print("链表: ")
    current := ll.Head
    for current != nil {
        fmt.Printf("%d", current.Value)
        if current.Next != nil {
            fmt.Print(" -> ")
        }
        current = current.Next
    }
    fmt.Printf(" (大小: %d)\n", ll.Size)
}

// 二叉树节点
type TreeNode struct {
    Value int
    Left  *TreeNode
    Right *TreeNode
}

// 插入节点
func (root **TreeNode) Insert(value int) {
    if *root == nil {
        *root = &TreeNode{Value: value}
        return
    }
    
    if value < (*root).Value {
        (*root).Left.Insert(value)
    } else {
        (*root).Right.Insert(value)
    }
}

// 中序遍历
func (root *TreeNode) InorderTraversal() {
    if root != nil {
        root.Left.InorderTraversal()
        fmt.Printf("%d ", root.Value)
        root.Right.InorderTraversal()
    }
}

// 指针的指针示例
func modifyPointer(ptr **int, newValue int) {
    *ptr = &newValue
}

func demonstrateAdvancedPointers() {
    fmt.Println("=== 指针的高级应用 ===")
    
    // 链表示例
    fmt.Println("\n--- 链表操作 ---")
    list := &LinkedList{}
    list.Add(1)
    list.Add(2)
    list.Add(3)
    list.Print()
    
    list.Remove(2)
    list.Print()
    
    // 二叉树示例
    fmt.Println("\n--- 二叉树操作 ---")
    var root *TreeNode
    values := []int{5, 3, 7, 1, 4, 6, 8}
    
    for _, value := range values {
        root.Insert(value)
    }
    
    fmt.Print("中序遍历: ")
    root.InorderTraversal()
    fmt.Println()
    
    // 指针的指针
    fmt.Println("\n--- 指针的指针 ---")
    original := 42
    ptr := &original
    fmt.Printf("原始值: %d, 指针指向: %d\n", original, *ptr)
    
    newValue := 100
    modifyPointer(&ptr, newValue)
    fmt.Printf("修改后指针指向: %d\n", *ptr)
}

// 函数指针示例
func add(a, b int) int {
    return a + b
}

func multiply(a, b int) int {
    return a * b
}

func demonstrateFunctionPointers() {
    fmt.Println("\n=== 函数指针 ===")
    
    // 函数类型
    type Operation func(int, int) int
    
    var op Operation
    
    // 赋值函数
    op = add
    fmt.Printf("加法: %d\n", op(10, 5))
    
    op = multiply
    fmt.Printf("乘法: %d\n", op(10, 5))
    
    // 函数切片
    operations := []Operation{add, multiply}
    operationNames := []string{"加法", "乘法"}
    
    for i, operation := range operations {
        result := operation(8, 3)
        fmt.Printf("%s: %d\n", operationNames[i], result)
    }
}

func main() {
    demonstrateAdvancedPointers()
    demonstrateFunctionPointers()
}
```

---

## 二、Go的内存分配机制

### 2.1 栈内存vs堆内存

```go
package main

import (
    "fmt"
    "runtime"
    "unsafe"
)

// 栈分配示例
func stackAllocation() int {
    // 局部变量通常分配在栈上
    var localVar int = 42
    var localArray [10]int
    
    fmt.Printf("局部变量地址: %p\n", &localVar)
    fmt.Printf("局部数组地址: %p\n", &localArray)
    
    return localVar
}

// 堆分配示例
func heapAllocation() *int {
    // 返回局部变量的地址会导致变量逃逸到堆上
    var localVar int = 42
    fmt.Printf("逃逸变量地址: %p\n", &localVar)
    return &localVar
}

// 大对象分配
func largeObjectAllocation() {
    // 大对象通常直接分配在堆上
    largeSlice := make([]int, 1000000)
    fmt.Printf("大切片地址: %p\n", &largeSlice[0])
    fmt.Printf("大切片长度: %d\n", len(largeSlice))
}

func demonstrateMemoryAllocation() {
    fmt.Println("=== 内存分配机制 ===")
    
    // 获取初始内存统计
    var m1 runtime.MemStats
    runtime.ReadMemStats(&m1)
    
    fmt.Printf("初始堆内存使用: %d KB\n", m1.Alloc/1024)
    
    // 栈分配
    fmt.Println("\n--- 栈分配 ---")
    result := stackAllocation()
    fmt.Printf("栈分配结果: %d\n", result)
    
    // 堆分配
    fmt.Println("\n--- 堆分配 ---")
    heapPtr := heapAllocation()
    fmt.Printf("堆分配结果: %d\n", *heapPtr)
    
    // 大对象分配
    fmt.Println("\n--- 大对象分配 ---")
    largeObjectAllocation()
    
    // 获取分配后的内存统计
    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)
    
    fmt.Printf("分配后堆内存使用: %d KB\n", m2.Alloc/1024)
    fmt.Printf("内存分配次数: %d\n", m2.Mallocs)
    fmt.Printf("内存释放次数: %d\n", m2.Frees)
}

// 内存对齐示例
type SmallStruct struct {
    a bool  // 1 byte
    b int32 // 4 bytes
    c bool  // 1 byte
}

type OptimizedStruct struct {
    a bool  // 1 byte
    c bool  // 1 byte (紧邻a，减少填充)
    b int32 // 4 bytes
}

type LargeStruct struct {
    a bool    // 1 byte
    b int64   // 8 bytes
    c bool    // 1 byte
    d float64 // 8 bytes
}

func demonstrateMemoryAlignment() {
    fmt.Println("\n=== 内存对齐 ===")
    
    var small SmallStruct
    var optimized OptimizedStruct
    var large LargeStruct
    
    fmt.Printf("SmallStruct 大小: %d 字节\n", unsafe.Sizeof(small))
    fmt.Printf("OptimizedStruct 大小: %d 字节\n", unsafe.Sizeof(optimized))
    fmt.Printf("LargeStruct 大小: %d 字节\n", unsafe.Sizeof(large))
    
    // 字段偏移量
    fmt.Printf("\nSmallStruct 字段偏移:\n")
    fmt.Printf("  a: %d\n", unsafe.Offsetof(small.a))
    fmt.Printf("  b: %d\n", unsafe.Offsetof(small.b))
    fmt.Printf("  c: %d\n", unsafe.Offsetof(small.c))
    
    fmt.Printf("\nOptimizedStruct 字段偏移:\n")
    fmt.Printf("  a: %d\n", unsafe.Offsetof(optimized.a))
    fmt.Printf("  c: %d\n", unsafe.Offsetof(optimized.c))
    fmt.Printf("  b: %d\n", unsafe.Offsetof(optimized.b))
}

// 内存池示例
type ObjectPool struct {
    pool chan *Object
}

type Object struct {
    Data [1024]byte
}

func NewObjectPool(size int) *ObjectPool {
    return &ObjectPool{
        pool: make(chan *Object, size),
    }
}

func (p *ObjectPool) Get() *Object {
    select {
    case obj := <-p.pool:
        return obj
    default:
        return &Object{}
    }
}

func (p *ObjectPool) Put(obj *Object) {
    select {
    case p.pool <- obj:
    default:
        // 池满了，丢弃对象
    }
}

func demonstrateObjectPool() {
    fmt.Println("\n=== 对象池示例 ===")
    
    pool := NewObjectPool(10)
    
    // 获取对象
    obj1 := pool.Get()
    obj2 := pool.Get()
    
    fmt.Printf("对象1地址: %p\n", obj1)
    fmt.Printf("对象2地址: %p\n", obj2)
    
    // 归还对象
    pool.Put(obj1)
    pool.Put(obj2)
    
    // 再次获取（可能复用之前的对象）
    obj3 := pool.Get()
    obj4 := pool.Get()
    
    fmt.Printf("对象3地址: %p\n", obj3)
    fmt.Printf("对象4地址: %p\n", obj4)
    
    fmt.Printf("对象1和对象3是否相同: %t\n", obj1 == obj3)
    fmt.Printf("对象2和对象4是否相同: %t\n", obj2 == obj4)
}

func main() {
    demonstrateMemoryAllocation()
    demonstrateMemoryAlignment()
    demonstrateObjectPool()
}
```

---

## 三、垃圾回收机制详解

### 3.1 GC工作原理和性能调优

```go
package main

import (
    "fmt"
    "runtime"
    "runtime/debug"
    "time"
)

func demonstrateGCBasics() {
    fmt.Println("=== 垃圾回收基础 ===")

    // 获取GC统计信息
    var stats debug.GCStats
    debug.ReadGCStats(&stats)

    fmt.Printf("GC次数: %d\n", stats.NumGC)
    fmt.Printf("总暂停时间: %v\n", stats.PauseTotal)
    if len(stats.Pause) > 0 {
        fmt.Printf("最近一次暂停时间: %v\n", stats.Pause[0])
    }

    // 获取详细内存统计
    var m runtime.MemStats
    runtime.ReadMemStats(&m)

    fmt.Printf("\n=== 内存统计 ===\n")
    fmt.Printf("当前分配的堆内存: %d KB\n", m.Alloc/1024)
    fmt.Printf("累计分配的内存: %d KB\n", m.TotalAlloc/1024)
    fmt.Printf("系统内存: %d KB\n", m.Sys/1024)
    fmt.Printf("GC次数: %d\n", m.NumGC)
    fmt.Printf("强制GC次数: %d\n", m.NumForcedGC)
    fmt.Printf("GC CPU占用: %.2f%%\n", m.GCCPUFraction*100)
}

// 模拟内存分配和GC
func simulateMemoryAllocation() {
    fmt.Println("\n=== 模拟内存分配 ===")

    // 记录初始状态
    var m1 runtime.MemStats
    runtime.ReadMemStats(&m1)

    // 大量分配内存
    var slices [][]int
    for i := 0; i < 1000; i++ {
        slice := make([]int, 1000)
        slices = append(slices, slice)

        // 每100次分配检查一次内存状态
        if i%100 == 0 {
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            fmt.Printf("分配%d次后 - 堆内存: %d KB, GC次数: %d\n",
                       i, m.Alloc/1024, m.NumGC)
        }
    }

    // 手动触发GC
    fmt.Println("\n手动触发GC...")
    runtime.GC()

    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)

    fmt.Printf("GC后 - 堆内存: %d KB, GC次数: %d\n",
               m2.Alloc/1024, m2.NumGC)

    // 释放引用
    slices = nil
    runtime.GC()

    var m3 runtime.MemStats
    runtime.ReadMemStats(&m3)

    fmt.Printf("释放引用后 - 堆内存: %d KB, GC次数: %d\n",
               m3.Alloc/1024, m3.NumGC)
}

// GC调优示例
func demonstrateGCTuning() {
    fmt.Println("\n=== GC调优 ===")

    // 获取当前GC目标百分比
    gcPercent := debug.SetGCPercent(-1) // -1表示获取当前值
    debug.SetGCPercent(gcPercent)       // 恢复原值

    fmt.Printf("当前GC目标百分比: %d%%\n", gcPercent)

    // 设置不同的GC目标
    fmt.Println("\n测试不同GC设置的影响:")

    testGCPercent := []int{50, 100, 200}

    for _, percent := range testGCPercent {
        fmt.Printf("\n--- 设置GC百分比为 %d%% ---\n", percent)

        oldPercent := debug.SetGCPercent(percent)

        // 模拟工作负载
        start := time.Now()
        var data [][]byte
        for i := 0; i < 1000; i++ {
            data = append(data, make([]byte, 1024))
        }

        var m runtime.MemStats
        runtime.ReadMemStats(&m)

        duration := time.Since(start)
        fmt.Printf("分配时间: %v\n", duration)
        fmt.Printf("GC次数: %d\n", m.NumGC)
        fmt.Printf("堆内存: %d KB\n", m.Alloc/1024)

        // 清理
        data = nil
        runtime.GC()

        // 恢复原设置
        debug.SetGCPercent(oldPercent)
    }
}

// 内存泄漏检测示例
func demonstrateMemoryLeak() {
    fmt.Println("\n=== 内存泄漏检测 ===")

    // 模拟内存泄漏场景
    var leakedData [][]byte

    fmt.Println("模拟内存泄漏...")
    for i := 0; i < 100; i++ {
        // 分配内存但不释放引用
        data := make([]byte, 1024*1024) // 1MB
        leakedData = append(leakedData, data)

        if i%20 == 0 {
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            fmt.Printf("迭代%d - 堆内存: %d MB\n", i, m.Alloc/1024/1024)
        }
    }

    fmt.Println("\n检测到内存持续增长，可能存在内存泄漏")

    // 修复泄漏
    fmt.Println("释放泄漏的内存...")
    leakedData = nil
    runtime.GC()

    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    fmt.Printf("修复后堆内存: %d MB\n", m.Alloc/1024/1024)
}

// 监控GC性能
func monitorGCPerformance() {
    fmt.Println("\n=== GC性能监控 ===")

    // 设置GC监控
    var lastGC uint32

    ticker := time.NewTicker(1 * time.Second)
    defer ticker.Stop()

    done := make(chan bool)
    go func() {
        time.Sleep(5 * time.Second)
        done <- true
    }()

    // 模拟工作负载
    go func() {
        for {
            select {
            case <-done:
                return
            default:
                // 持续分配内存
                data := make([]byte, 1024)
                _ = data
                time.Sleep(1 * time.Millisecond)
            }
        }
    }()

    // 监控循环
    for {
        select {
        case <-ticker.C:
            var m runtime.MemStats
            runtime.ReadMemStats(&m)

            if m.NumGC > lastGC {
                fmt.Printf("GC发生 - 次数: %d, 堆内存: %d KB, 暂停时间: %v\n",
                           m.NumGC, m.Alloc/1024, time.Duration(m.PauseNs[(m.NumGC+255)%256]))
                lastGC = m.NumGC
            }

        case <-done:
            fmt.Println("监控结束")
            return
        }
    }
}

func main() {
    demonstrateGCBasics()
    simulateMemoryAllocation()
    demonstrateGCTuning()
    demonstrateMemoryLeak()
    monitorGCPerformance()
}
```

---

## 四、内存泄漏的识别和预防

### 4.1 常见内存泄漏场景

```go
package main

import (
    "context"
    "fmt"
    "runtime"
    "sync"
    "time"
)

// 场景1：goroutine泄漏
func demonstrateGoroutineLeak() {
    fmt.Println("=== Goroutine泄漏示例 ===")

    // 错误示例：goroutine永远不会结束
    fmt.Println("创建可能泄漏的goroutine...")

    for i := 0; i < 10; i++ {
        go func(id int) {
            // 这个goroutine会一直运行，造成泄漏
            for {
                time.Sleep(1 * time.Second)
                // 没有退出条件
            }
        }(i)
    }

    // 检查goroutine数量
    fmt.Printf("当前goroutine数量: %d\n", runtime.NumGoroutine())

    // 正确示例：使用context控制goroutine生命周期
    fmt.Println("\n使用context控制goroutine...")

    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()

    for i := 0; i < 5; i++ {
        go func(id int, ctx context.Context) {
            for {
                select {
                case <-ctx.Done():
                    fmt.Printf("Goroutine %d 正常退出\n", id)
                    return
                default:
                    time.Sleep(500 * time.Millisecond)
                }
            }
        }(i, ctx)
    }

    time.Sleep(4 * time.Second)
    fmt.Printf("清理后goroutine数量: %d\n", runtime.NumGoroutine())
}

// 场景2：闭包引用泄漏
func demonstrateClosureLeak() {
    fmt.Println("\n=== 闭包引用泄漏 ===")

    // 错误示例：闭包持有大对象引用
    var functions []func() int

    for i := 0; i < 10; i++ {
        largeData := make([]int, 1000000) // 大对象
        largeData[0] = i

        // 闭包捕获了整个largeData
        functions = append(functions, func() int {
            return largeData[0] // 只使用第一个元素，但整个数组被保留
        })
    }

    var m1 runtime.MemStats
    runtime.ReadMemStats(&m1)
    fmt.Printf("闭包创建后内存: %d MB\n", m1.Alloc/1024/1024)

    // 正确示例：只捕获需要的值
    var optimizedFunctions []func() int

    for i := 0; i < 10; i++ {
        largeData := make([]int, 1000000)
        largeData[0] = i

        // 只捕获需要的值
        value := largeData[0]
        optimizedFunctions = append(optimizedFunctions, func() int {
            return value
        })
        // largeData在循环结束后可以被GC
    }

    // 清理第一组函数
    functions = nil
    runtime.GC()

    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)
    fmt.Printf("优化后内存: %d MB\n", m2.Alloc/1024/1024)
}

// 场景3：map泄漏
func demonstrateMapLeak() {
    fmt.Println("\n=== Map泄漏示例 ===")

    // 模拟缓存场景
    cache := make(map[string][]byte)

    // 不断添加数据到map
    fmt.Println("向map添加数据...")
    for i := 0; i < 1000; i++ {
        key := fmt.Sprintf("key_%d", i)
        value := make([]byte, 1024) // 1KB数据
        cache[key] = value
    }

    var m1 runtime.MemStats
    runtime.ReadMemStats(&m1)
    fmt.Printf("添加数据后内存: %d KB\n", m1.Alloc/1024)

    // 错误的清理方式：只删除部分key
    fmt.Println("删除部分数据...")
    for i := 0; i < 500; i++ {
        key := fmt.Sprintf("key_%d", i)
        delete(cache, key)
    }

    runtime.GC()
    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)
    fmt.Printf("部分删除后内存: %d KB\n", m2.Alloc/1024)

    // 正确的清理方式：重新创建map
    fmt.Println("重新创建map...")
    cache = make(map[string][]byte)

    runtime.GC()
    var m3 runtime.MemStats
    runtime.ReadMemStats(&m3)
    fmt.Printf("重新创建后内存: %d KB\n", m3.Alloc/1024)
}

// 场景4：切片泄漏
func demonstrateSliceLeak() {
    fmt.Println("\n=== 切片泄漏示例 ===")

    // 创建大切片
    largeSlice := make([]int, 1000000)
    for i := range largeSlice {
        largeSlice[i] = i
    }

    var m1 runtime.MemStats
    runtime.ReadMemStats(&m1)
    fmt.Printf("大切片创建后内存: %d MB\n", m1.Alloc/1024/1024)

    // 错误示例：子切片仍然引用原始数组
    smallSlice := largeSlice[:10] // 只需要前10个元素
    largeSlice = nil              // 试图释放大切片

    runtime.GC()
    var m2 runtime.MemStats
    runtime.ReadMemStats(&m2)
    fmt.Printf("子切片引用后内存: %d MB\n", m2.Alloc/1024/1024)

    // 正确示例：复制需要的数据
    correctSlice := make([]int, 10)
    copy(correctSlice, smallSlice)
    smallSlice = nil // 释放对原始数组的引用

    runtime.GC()
    var m3 runtime.MemStats
    runtime.ReadMemStats(&m3)
    fmt.Printf("正确处理后内存: %d MB\n", m3.Alloc/1024/1024)
}

// 内存泄漏检测工具
type MemoryMonitor struct {
    samples []runtime.MemStats
    mu      sync.Mutex
}

func NewMemoryMonitor() *MemoryMonitor {
    return &MemoryMonitor{
        samples: make([]runtime.MemStats, 0),
    }
}

func (m *MemoryMonitor) Sample() {
    m.mu.Lock()
    defer m.mu.Unlock()

    var stats runtime.MemStats
    runtime.ReadMemStats(&stats)
    m.samples = append(m.samples, stats)
}

func (m *MemoryMonitor) DetectLeak() bool {
    m.mu.Lock()
    defer m.mu.Unlock()

    if len(m.samples) < 3 {
        return false
    }

    // 简单的泄漏检测：内存持续增长
    recent := m.samples[len(m.samples)-3:]

    for i := 1; i < len(recent); i++ {
        if recent[i].Alloc <= recent[i-1].Alloc {
            return false
        }
    }

    // 如果最近3次采样内存都在增长，可能存在泄漏
    growth := recent[2].Alloc - recent[0].Alloc
    return growth > 1024*1024 // 增长超过1MB
}

func (m *MemoryMonitor) Report() {
    m.mu.Lock()
    defer m.mu.Unlock()

    if len(m.samples) == 0 {
        return
    }

    latest := m.samples[len(m.samples)-1]
    fmt.Printf("内存监控报告:\n")
    fmt.Printf("  当前内存: %d KB\n", latest.Alloc/1024)
    fmt.Printf("  GC次数: %d\n", latest.NumGC)
    fmt.Printf("  采样次数: %d\n", len(m.samples))

    if m.DetectLeak() {
        fmt.Printf("  ⚠️  检测到可能的内存泄漏\n")
    } else {
        fmt.Printf("  ✅ 内存使用正常\n")
    }
}

func demonstrateMemoryMonitoring() {
    fmt.Println("\n=== 内存监控示例 ===")

    monitor := NewMemoryMonitor()

    // 模拟应用运行
    for i := 0; i < 10; i++ {
        // 模拟内存分配
        data := make([]byte, 1024*100) // 100KB
        _ = data

        monitor.Sample()
        time.Sleep(100 * time.Millisecond)
    }

    monitor.Report()
}

func main() {
    demonstrateGoroutineLeak()
    demonstrateClosureLeak()
    demonstrateMapLeak()
    demonstrateSliceLeak()
    demonstrateMemoryMonitoring()
}
```
