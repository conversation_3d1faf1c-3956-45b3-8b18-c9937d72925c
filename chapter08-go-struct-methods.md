# 第8章：结构体与方法

## 章节概述

在掌握了Go语言的指针与内存管理后，我们现在要学习Go语言面向对象编程的核心——结构体与方法。如果说指针是程序的"血液循环系统"，那么结构体与方法就是程序的"器官系统"，它们将数据和行为有机地组织在一起，构成了程序的基本功能单元。

### 学习目标
- 深入理解结构体的定义、初始化和使用方法
- 掌握方法的定义和接收者类型的选择原则
- 理解结构体组合和嵌入的强大功能
- 学会使用结构体标签进行数据序列化和映射
- 掌握方法集概念和接口实现机制
- 了解结构体的内存布局和性能优化技巧
- 培养面向对象设计的思维和Go语言的设计哲学

### 结构体与方法的重要性
结构体与方法是Go语言面向对象编程的基石，它们：
- **组织数据和行为**：将相关的数据和操作封装在一起
- **实现代码复用**：通过方法提供可重用的功能
- **支持多态机制**：为接口实现提供基础
- **体现设计哲学**：展现Go语言"组合优于继承"的设计理念
- **连接底层与抽象**：将前面学习的指针、内存等底层概念抽象为高层设计
- **支持大型项目**：为构建复杂系统提供结构化的组织方式

Go语言的结构体不同于传统面向对象语言的类，它更加简洁和灵活。Go没有类的概念，但通过结构体和方法的组合，实现了面向对象编程的核心特性。这种设计体现了Go语言"简单而强大"的设计哲学。

---

## 一、结构体的定义、初始化和嵌套使用

### 1.1 结构体的基本定义和语法

结构体是Go语言中用户自定义类型的主要方式，它将多个不同类型的字段组合成一个复合类型。结构体的定义使用`type`关键字和`struct`关键字。

```go
package main

import (
    "fmt"
    "time"
)

// 基本结构体定义
type Person struct {
    Name    string    // 姓名
    Age     int       // 年龄
    Email   string    // 邮箱
    Height  float64   // 身高（米）
    IsActive bool     // 是否活跃
}

// 包含时间字段的结构体
type Employee struct {
    ID          int       // 员工ID
    Name        string    // 姓名
    Department  string    // 部门
    Salary      float64   // 薪资
    HireDate    time.Time // 入职日期
    IsManager   bool      // 是否为管理者
}

// 嵌套结构体
type Address struct {
    Street   string // 街道
    City     string // 城市
    Province string // 省份
    ZipCode  string // 邮编
}

type Customer struct {
    Person          // 嵌入Person结构体
    CustomerID string
    Address    Address // 包含Address结构体
    Orders     []Order // 订单切片
}

type Order struct {
    OrderID   string    // 订单ID
    Amount    float64   // 金额
    OrderDate time.Time // 订单日期
    Status    string    // 状态
}

func demonstrateStructBasics() {
    fmt.Println("=== 结构体基础操作 ===")
    
    // 方式1：零值初始化
    var p1 Person
    fmt.Printf("零值初始化: %+v\n", p1)
    
    // 方式2：字面量初始化（按字段顺序）
    p2 := Person{"张三", 30, "<EMAIL>", 1.75, true}
    fmt.Printf("按顺序初始化: %+v\n", p2)
    
    // 方式3：字面量初始化（指定字段名）
    p3 := Person{
        Name:     "李四",
        Age:      25,
        Email:    "<EMAIL>",
        Height:   1.68,
        IsActive: true,
    }
    fmt.Printf("指定字段初始化: %+v\n", p3)
    
    // 方式4：部分字段初始化
    p4 := Person{
        Name:  "王五",
        Age:   35,
        Email: "<EMAIL>",
        // Height和IsActive使用零值
    }
    fmt.Printf("部分字段初始化: %+v\n", p4)
    
    // 字段访问和修改
    fmt.Printf("\n=== 字段操作 ===\n")
    fmt.Printf("修改前: %s, 年龄: %d\n", p3.Name, p3.Age)
    p3.Age = 26
    p3.Email = "<EMAIL>"
    fmt.Printf("修改后: %s, 年龄: %d, 邮箱: %s\n", p3.Name, p3.Age, p3.Email)
}

func demonstrateNestedStructs() {
    fmt.Println("\n=== 嵌套结构体 ===")
    
    // 创建嵌套结构体
    customer := Customer{
        Person: Person{
            Name:     "赵六",
            Age:      28,
            Email:    "<EMAIL>",
            Height:   1.72,
            IsActive: true,
        },
        CustomerID: "CUST001",
        Address: Address{
            Street:   "中关村大街1号",
            City:     "北京",
            Province: "北京市",
            ZipCode:  "100080",
        },
        Orders: []Order{
            {
                OrderID:   "ORD001",
                Amount:    299.99,
                OrderDate: time.Now().AddDate(0, 0, -7),
                Status:    "已完成",
            },
            {
                OrderID:   "ORD002",
                Amount:    599.99,
                OrderDate: time.Now().AddDate(0, 0, -3),
                Status:    "处理中",
            },
        },
    }
    
    // 访问嵌套字段
    fmt.Printf("客户信息: %s (ID: %s)\n", customer.Name, customer.CustomerID)
    fmt.Printf("地址: %s, %s, %s %s\n", 
               customer.Address.Street, 
               customer.Address.City, 
               customer.Address.Province, 
               customer.Address.ZipCode)
    
    fmt.Printf("订单数量: %d\n", len(customer.Orders))
    for i, order := range customer.Orders {
        fmt.Printf("  订单%d: %s, 金额: %.2f, 状态: %s\n", 
                   i+1, order.OrderID, order.Amount, order.Status)
    }
}

func main() {
    demonstrateStructBasics()
    demonstrateNestedStructs()
}
```

### 1.2 构造函数模式和工厂方法

Go语言没有构造函数的概念，但我们可以通过函数来模拟构造函数的功能，这是Go语言的惯用法。

```go
package main

import (
    "fmt"
    "strings"
    "time"
)

// 用户结构体
type User struct {
    id       int       // 私有字段（小写开头）
    username string    // 私有字段
    email    string    // 私有字段
    password string    // 私有字段
    created  time.Time // 私有字段
    updated  time.Time // 私有字段
}

// 构造函数模式 - 基本构造函数
func NewUser(username, email, password string) (*User, error) {
    // 输入验证
    if strings.TrimSpace(username) == "" {
        return nil, fmt.Errorf("用户名不能为空")
    }
    
    if !strings.Contains(email, "@") {
        return nil, fmt.Errorf("邮箱格式不正确")
    }
    
    if len(password) < 6 {
        return nil, fmt.Errorf("密码长度至少6位")
    }
    
    // 生成ID（简化实现）
    id := int(time.Now().Unix() % 100000)
    
    return &User{
        id:       id,
        username: username,
        email:    email,
        password: password, // 实际应用中应该加密
        created:  time.Now(),
        updated:  time.Now(),
    }, nil
}

// 选项模式构造函数
type UserOption func(*User)

func WithID(id int) UserOption {
    return func(u *User) {
        u.id = id
    }
}

func WithCreatedTime(t time.Time) UserOption {
    return func(u *User) {
        u.created = t
    }
}

func NewUserWithOptions(username, email, password string, options ...UserOption) (*User, error) {
    // 基本验证
    user, err := NewUser(username, email, password)
    if err != nil {
        return nil, err
    }
    
    // 应用选项
    for _, option := range options {
        option(user)
    }
    
    return user, nil
}

// Getter方法（访问私有字段）
func (u *User) ID() int {
    return u.id
}

func (u *User) Username() string {
    return u.username
}

func (u *User) Email() string {
    return u.email
}

func (u *User) Created() time.Time {
    return u.created
}

// Setter方法（修改私有字段）
func (u *User) SetEmail(email string) error {
    if !strings.Contains(email, "@") {
        return fmt.Errorf("邮箱格式不正确")
    }
    u.email = email
    u.updated = time.Now()
    return nil
}

func (u *User) SetPassword(password string) error {
    if len(password) < 6 {
        return fmt.Errorf("密码长度至少6位")
    }
    u.password = password
    u.updated = time.Now()
    return nil
}

// 字符串表示方法
func (u *User) String() string {
    return fmt.Sprintf("User{ID: %d, Username: %s, Email: %s, Created: %s}", 
                       u.id, u.username, u.email, u.created.Format("2006-01-02"))
}

// 工厂方法模式
type UserType int

const (
    RegularUser UserType = iota
    AdminUser
    GuestUser
)

func CreateUser(userType UserType, username, email string) (*User, error) {
    switch userType {
    case RegularUser:
        return NewUser(username, email, "default123")
    case AdminUser:
        user, err := NewUser(username, email, "admin123")
        if err != nil {
            return nil, err
        }
        // 管理员用户的特殊设置
        user.id = 1000 + user.id
        return user, nil
    case GuestUser:
        return NewUser("guest_"+username, email, "guest123")
    default:
        return nil, fmt.Errorf("不支持的用户类型")
    }
}

func demonstrateConstructorPatterns() {
    fmt.Println("=== 构造函数模式 ===")
    
    // 基本构造函数
    user1, err := NewUser("张三", "<EMAIL>", "password123")
    if err != nil {
        fmt.Printf("创建用户失败: %v\n", err)
        return
    }
    fmt.Printf("用户1: %s\n", user1)
    
    // 选项模式构造函数
    user2, err := NewUserWithOptions(
        "李四", 
        "<EMAIL>", 
        "password456",
        WithID(2001),
        WithCreatedTime(time.Now().AddDate(-1, 0, 0)),
    )
    if err != nil {
        fmt.Printf("创建用户失败: %v\n", err)
        return
    }
    fmt.Printf("用户2: %s\n", user2)
    
    // 工厂方法
    adminUser, err := CreateUser(AdminUser, "admin", "<EMAIL>")
    if err != nil {
        fmt.Printf("创建管理员失败: %v\n", err)
        return
    }
    fmt.Printf("管理员: %s\n", adminUser)
    
    // 使用Getter和Setter
    fmt.Printf("\n=== Getter/Setter操作 ===\n")
    fmt.Printf("用户ID: %d\n", user1.ID())
    fmt.Printf("用户名: %s\n", user1.Username())
    
    err = user1.SetEmail("<EMAIL>")
    if err != nil {
        fmt.Printf("设置邮箱失败: %v\n", err)
    } else {
        fmt.Printf("更新后邮箱: %s\n", user1.Email())
    }
}

func main() {
    demonstrateConstructorPatterns()
}
```

---

## 二、方法的定义和接收者类型

### 2.1 方法的基本语法和接收者概念

Go语言的方法是带有特殊接收者参数的函数。接收者出现在`func`关键字和方法名之间。方法可以为任何类型定义，不仅仅是结构体。

```go
package main

import (
    "fmt"
    "math"
    "strings"
)

// 几何图形接口和实现
type Rectangle struct {
    Width  float64
    Height float64
}

type Circle struct {
    Radius float64
}

// Rectangle的方法 - 值接收者
func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}

func (r Rectangle) Perimeter() float64 {
    return 2 * (r.Width + r.Height)
}

// Rectangle的方法 - 指针接收者
func (r *Rectangle) Scale(factor float64) {
    r.Width *= factor
    r.Height *= factor
}

func (r *Rectangle) SetDimensions(width, height float64) {
    r.Width = width
    r.Height = height
}

// Circle的方法
func (c Circle) Area() float64 {
    return math.Pi * c.Radius * c.Radius
}

func (c Circle) Perimeter() float64 {
    return 2 * math.Pi * c.Radius
}

func (c *Circle) Scale(factor float64) {
    c.Radius *= factor
}

// 字符串方法
func (r Rectangle) String() string {
    return fmt.Sprintf("Rectangle{Width: %.2f, Height: %.2f}", r.Width, r.Height)
}

func (c Circle) String() string {
    return fmt.Sprintf("Circle{Radius: %.2f}", c.Radius)
}

// 自定义类型的方法
type MyString string

func (s MyString) Length() int {
    return len(s)
}

func (s MyString) Upper() MyString {
    return MyString(strings.ToUpper(string(s)))
}

func (s MyString) Contains(substr string) bool {
    return strings.Contains(string(s), substr)
}

func (s *MyString) Append(text string) {
    *s = MyString(string(*s) + text)
}

func demonstrateMethodBasics() {
    fmt.Println("=== 方法基础操作 ===")
    
    // 创建Rectangle实例
    rect := Rectangle{Width: 10, Height: 5}
    fmt.Printf("矩形: %s\n", rect)
    fmt.Printf("面积: %.2f\n", rect.Area())
    fmt.Printf("周长: %.2f\n", rect.Perimeter())
    
    // 使用指针接收者方法
    fmt.Printf("\n=== 指针接收者方法 ===\n")
    fmt.Printf("缩放前: %s\n", rect)
    rect.Scale(2.0) // Go会自动取地址
    fmt.Printf("缩放后: %s\n", rect)
    
    // 创建Circle实例
    circle := Circle{Radius: 3.0}
    fmt.Printf("\n圆形: %s\n", circle)
    fmt.Printf("面积: %.2f\n", circle.Area())
    fmt.Printf("周长: %.2f\n", circle.Perimeter())
    
    // 自定义字符串类型
    fmt.Printf("\n=== 自定义类型方法 ===\n")
    var myStr MyString = "Hello, Go!"
    fmt.Printf("原字符串: %s\n", myStr)
    fmt.Printf("长度: %d\n", myStr.Length())
    fmt.Printf("大写: %s\n", myStr.Upper())
    fmt.Printf("包含'Go': %t\n", myStr.Contains("Go"))
    
    myStr.Append(" Welcome!")
    fmt.Printf("追加后: %s\n", myStr)
}

func main() {
    demonstrateMethodBasics()
}
```

### 2.2 值接收者vs指针接收者的选择原则

选择值接收者还是指针接收者是Go语言中的重要决策，这个选择影响性能、语义和方法集。

```go
package main

import (
    "fmt"
    "time"
)

// 演示值接收者vs指针接收者的区别
type Counter struct {
    value int
    name  string
}

// 值接收者方法 - 不会修改原始值
func (c Counter) GetValue() int {
    return c.value
}

func (c Counter) GetName() string {
    return c.name
}

// 值接收者方法 - 尝试修改（无效）
func (c Counter) IncrementByValue() {
    c.value++ // 只修改副本，不影响原始值
    fmt.Printf("值接收者内部: %d\n", c.value)
}

// 指针接收者方法 - 可以修改原始值
func (c *Counter) Increment() {
    c.value++
}

func (c *Counter) IncrementBy(amount int) {
    c.value += amount
}

func (c *Counter) Reset() {
    c.value = 0
}

func (c *Counter) SetName(name string) {
    c.name = name
}

// 大结构体示例
type LargeStruct struct {
    data [1000]int
    name string
    id   int
}

// 值接收者 - 会复制整个结构体（性能差）
func (ls LargeStruct) ProcessByValue() string {
    // 处理大量数据...
    return fmt.Sprintf("处理完成: %s", ls.name)
}

// 指针接收者 - 只传递指针（性能好）
func (ls *LargeStruct) ProcessByPointer() string {
    // 处理大量数据...
    return fmt.Sprintf("处理完成: %s", ls.name)
}

// 接收者选择的最佳实践示例
type BankAccount struct {
    accountNumber string
    balance       float64
    owner         string
    transactions  []Transaction
}

type Transaction struct {
    amount      float64
    description string
    timestamp   time.Time
}

// 查询方法 - 使用值接收者（不修改状态）
func (ba BankAccount) GetBalance() float64 {
    return ba.balance
}

func (ba BankAccount) GetOwner() string {
    return ba.owner
}

func (ba BankAccount) GetAccountNumber() string {
    return ba.accountNumber
}

// 修改方法 - 使用指针接收者（修改状态）
func (ba *BankAccount) Deposit(amount float64) error {
    if amount <= 0 {
        return fmt.Errorf("存款金额必须大于0")
    }
    
    ba.balance += amount
    ba.addTransaction(amount, "存款")
    return nil
}

func (ba *BankAccount) Withdraw(amount float64) error {
    if amount <= 0 {
        return fmt.Errorf("取款金额必须大于0")
    }
    
    if amount > ba.balance {
        return fmt.Errorf("余额不足")
    }
    
    ba.balance -= amount
    ba.addTransaction(-amount, "取款")
    return nil
}

func (ba *BankAccount) addTransaction(amount float64, description string) {
    transaction := Transaction{
        amount:      amount,
        description: description,
        timestamp:   time.Now(),
    }
    ba.transactions = append(ba.transactions, transaction)
}

// 字符串表示 - 使用值接收者（不修改状态）
func (ba BankAccount) String() string {
    return fmt.Sprintf("账户[%s] 户主: %s, 余额: %.2f", 
                       ba.accountNumber, ba.owner, ba.balance)
}

func demonstrateReceiverTypes() {
    fmt.Println("=== 接收者类型对比 ===")
    
    // Counter示例
    counter := Counter{value: 0, name: "测试计数器"}
    fmt.Printf("初始计数器: value=%d\n", counter.GetValue())
    
    // 值接收者 - 不会修改原始值
    counter.IncrementByValue()
    fmt.Printf("值接收者调用后: value=%d\n", counter.GetValue())
    
    // 指针接收者 - 会修改原始值
    counter.Increment()
    fmt.Printf("指针接收者调用后: value=%d\n", counter.GetValue())
    
    counter.IncrementBy(5)
    fmt.Printf("增加5后: value=%d\n", counter.GetValue())
    
    // 银行账户示例
    fmt.Printf("\n=== 银行账户示例 ===\n")
    account := &BankAccount{
        accountNumber: "ACC001",
        balance:       1000.0,
        owner:         "张三",
        transactions:  make([]Transaction, 0),
    }
    
    fmt.Printf("初始状态: %s\n", account)
    
    // 存款
    err := account.Deposit(500.0)
    if err != nil {
        fmt.Printf("存款失败: %v\n", err)
    } else {
        fmt.Printf("存款后: %s\n", account)
    }
    
    // 取款
    err = account.Withdraw(200.0)
    if err != nil {
        fmt.Printf("取款失败: %v\n", err)
    } else {
        fmt.Printf("取款后: %s\n", account)
    }
    
    // 尝试超额取款
    err = account.Withdraw(2000.0)
    if err != nil {
        fmt.Printf("超额取款: %v\n", err)
    }
}

// 接收者选择指导原则
func demonstrateReceiverGuidelines() {
    fmt.Println("\n=== 接收者选择指导原则 ===")
    
    fmt.Println("使用指针接收者的情况：")
    fmt.Println("1. 方法需要修改接收者")
    fmt.Println("2. 接收者是大型结构体（避免复制开销）")
    fmt.Println("3. 接收者包含sync.Mutex等不可复制的字段")
    fmt.Println("4. 为了保持方法集的一致性")
    
    fmt.Println("\n使用值接收者的情况：")
    fmt.Println("1. 方法不修改接收者")
    fmt.Println("2. 接收者是小型结构体或基本类型")
    fmt.Println("3. 接收者是map、func、chan等引用类型")
    fmt.Println("4. 接收者是不可变类型")
}

func main() {
    demonstrateReceiverTypes()
    demonstrateReceiverGuidelines()
}
```
